INFO 05-14 15:00:02 [__init__.py:239] Automatically detected platform cuda.
INFO 05-14 15:00:14 [api_server.py:1043] vLLM API server version 0.8.5.post1
INFO 05-14 15:00:14 [api_server.py:1044] args: Namespace(subparser='serve', model_tag='Qwen/Qwen3-8B', config='', host='0.0.0.0', port=8701, uvicorn_log_level='info', disable_uvicorn_access_log=False, allow_credentials=False, allowed_origins=['*'], allowed_methods=['*'], allowed_headers=['*'], api_key='EMPTY', lora_modules=None, prompt_adapters=None, chat_template=None, chat_template_content_format='auto', response_role='assistant', ssl_keyfile=None, ssl_certfile=None, ssl_ca_certs=None, enable_ssl_refresh=False, ssl_cert_reqs=0, root_path=None, middleware=[], return_tokens_as_token_ids=False, disable_frontend_multiprocessing=False, enable_request_id_headers=False, enable_auto_tool_choice=True, tool_call_parser='hermes', tool_parser_plugin='', model='Qwen/Qwen3-8B', task='auto', tokenizer=None, hf_config_path=None, skip_tokenizer_init=False, revision=None, code_revision=None, tokenizer_revision=None, tokenizer_mode='auto', trust_remote_code=False, allowed_local_media_path=None, load_format='auto', download_dir=None, model_loader_extra_config={}, use_tqdm_on_load=True, config_format=<ConfigFormat.AUTO: 'auto'>, dtype='auto', max_model_len=None, guided_decoding_backend='auto', reasoning_parser='deepseek_r1', logits_processor_pattern=None, model_impl='auto', distributed_executor_backend=None, pipeline_parallel_size=1, tensor_parallel_size=1, data_parallel_size=1, enable_expert_parallel=False, max_parallel_loading_workers=None, ray_workers_use_nsight=False, disable_custom_all_reduce=False, block_size=None, gpu_memory_utilization=0.9, swap_space=4, kv_cache_dtype='auto', num_gpu_blocks_override=None, enable_prefix_caching=None, prefix_caching_hash_algo='builtin', cpu_offload_gb=0, calculate_kv_scales=False, disable_sliding_window=False, use_v2_block_manager=True, seed=None, max_logprobs=20, disable_log_stats=False, quantization=None, rope_scaling=None, rope_theta=None, hf_token=None, hf_overrides=None, enforce_eager=False, max_seq_len_to_capture=8192, tokenizer_pool_size=0, tokenizer_pool_type='ray', tokenizer_pool_extra_config={}, limit_mm_per_prompt={}, mm_processor_kwargs=None, disable_mm_preprocessor_cache=False, enable_lora=None, enable_lora_bias=False, max_loras=1, max_lora_rank=16, lora_extra_vocab_size=256, lora_dtype='auto', long_lora_scaling_factors=None, max_cpu_loras=None, fully_sharded_loras=False, enable_prompt_adapter=None, max_prompt_adapters=1, max_prompt_adapter_token=0, device='auto', speculative_config=None, ignore_patterns=[], served_model_name=None, qlora_adapter_name_or_path=None, show_hidden_metrics_for_version=None, otlp_traces_endpoint=None, collect_detailed_traces=None, disable_async_output_proc=False, max_num_batched_tokens=None, max_num_seqs=None, max_num_partial_prefills=1, max_long_partial_prefills=1, long_prefill_token_threshold=0, num_lookahead_slots=0, scheduler_delay_factor=0.0, preemption_mode=None, num_scheduler_steps=1, multi_step_stream_outputs=True, scheduling_policy='fcfs', enable_chunked_prefill=None, disable_chunked_mm_input=False, scheduler_cls='vllm.core.scheduler.Scheduler', override_neuron_config=None, override_pooler_config=None, compilation_config=None, kv_transfer_config=None, worker_cls='auto', worker_extension_cls='', generation_config='auto', override_generation_config=None, enable_sleep_mode=False, additional_config=None, enable_reasoning=True, disable_cascade_attn=False, disable_log_requests=False, max_log_len=None, disable_fastapi_docs=False, enable_prompt_tokens_details=False, enable_server_load_tracking=False, dispatch_function=<function ServeSubcommand.cmd at 0x7f337583ade0>)
INFO 05-14 15:00:36 [config.py:717] This model supports multiple tasks: {'classify', 'generate', 'score', 'reward', 'embed'}. Defaulting to 'generate'.
INFO 05-14 15:00:46 [config.py:2003] Chunked prefill is enabled with max_num_batched_tokens=2048.
INFO 05-14 15:00:57 [__init__.py:239] Automatically detected platform cuda.
INFO 05-14 15:01:07 [core.py:58] Initializing a V1 LLM engine (v0.8.5.post1) with config: model='Qwen/Qwen3-8B', speculative_config=None, tokenizer='Qwen/Qwen3-8B', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.bfloat16, max_seq_len=40960, download_dir=None, load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto,  device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='auto', reasoning_backend='deepseek_r1'), observability_config=ObservabilityConfig(show_hidden_metrics=False, otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=None, served_model_name=Qwen/Qwen3-8B, num_scheduler_steps=1, multi_step_stream_outputs=True, enable_prefix_caching=True, chunked_prefill_enabled=True, use_async_output_proc=True, disable_mm_preprocessor_cache=False, mm_processor_kwargs=None, pooler_config=None, compilation_config={"level":3,"custom_ops":["none"],"splitting_ops":["vllm.unified_attention","vllm.unified_attention_with_output"],"use_inductor":true,"compile_sizes":[],"use_cudagraph":true,"cudagraph_num_of_warmups":1,"cudagraph_capture_sizes":[512,504,496,488,480,472,464,456,448,440,432,424,416,408,400,392,384,376,368,360,352,344,336,328,320,312,304,296,288,280,272,264,256,248,240,232,224,216,208,200,192,184,176,168,160,152,144,136,128,120,112,104,96,88,80,72,64,56,48,40,32,24,16,8,4,2,1],"max_capture_size":512}
WARNING 05-14 15:01:07 [utils.py:2522] Methods determine_num_available_blocks,device_config,get_cache_block_size_bytes,initialize_cache not implemented in <vllm.v1.worker.gpu_worker.Worker object at 0x7faad96edf70>
INFO 05-14 15:01:10 [parallel_state.py:1004] rank 0 in world size 1 is assigned as DP rank 0, PP rank 0, TP rank 0
INFO 05-14 15:01:10 [cuda.py:221] Using Flash Attention backend on V1 engine.
WARNING 05-14 15:01:10 [topk_topp_sampler.py:69] FlashInfer is not available. Falling back to the PyTorch-native implementation of top-p & top-k sampling. For the best performance, please install FlashInfer.
INFO 05-14 15:01:10 [gpu_model_runner.py:1329] Starting to load model Qwen/Qwen3-8B...
INFO 05-14 15:01:11 [weight_utils.py:265] Using model weights format ['*.safetensors']
ERROR 05-14 15:17:41 [core.py:396] EngineCore failed to start.
ERROR 05-14 15:17:41 [core.py:396] Traceback (most recent call last):
ERROR 05-14 15:17:41 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/v1/engine/core.py", line 387, in run_engine_core
ERROR 05-14 15:17:41 [core.py:396]     engine_core = EngineCoreProc(*args, **kwargs)
ERROR 05-14 15:17:41 [core.py:396]                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ERROR 05-14 15:17:41 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/v1/engine/core.py", line 329, in __init__
ERROR 05-14 15:17:41 [core.py:396]     super().__init__(vllm_config, executor_class, log_stats,
ERROR 05-14 15:17:41 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/v1/engine/core.py", line 64, in __init__
ERROR 05-14 15:17:41 [core.py:396]     self.model_executor = executor_class(vllm_config)
ERROR 05-14 15:17:41 [core.py:396]                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
ERROR 05-14 15:17:41 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/executor/executor_base.py", line 52, in __init__
ERROR 05-14 15:17:41 [core.py:396]     self._init_executor()
ERROR 05-14 15:17:41 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/executor/uniproc_executor.py", line 47, in _init_executor
ERROR 05-14 15:17:41 [core.py:396]     self.collective_rpc("load_model")
ERROR 05-14 15:17:41 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/executor/uniproc_executor.py", line 56, in collective_rpc
ERROR 05-14 15:17:41 [core.py:396]     answer = run_method(self.driver_worker, method, args, kwargs)
ERROR 05-14 15:17:41 [core.py:396]              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ERROR 05-14 15:17:41 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/utils.py", line 2456, in run_method
ERROR 05-14 15:17:41 [core.py:396]     return func(*args, **kwargs)
ERROR 05-14 15:17:41 [core.py:396]            ^^^^^^^^^^^^^^^^^^^^^
ERROR 05-14 15:17:41 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/v1/worker/gpu_worker.py", line 162, in load_model
ERROR 05-14 15:17:41 [core.py:396]     self.model_runner.load_model()
ERROR 05-14 15:17:41 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/v1/worker/gpu_model_runner.py", line 1332, in load_model
ERROR 05-14 15:17:41 [core.py:396]     self.model = get_model(vllm_config=self.vllm_config)
ERROR 05-14 15:17:41 [core.py:396]                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ERROR 05-14 15:17:41 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/model_loader/__init__.py", line 14, in get_model
ERROR 05-14 15:17:41 [core.py:396]     return loader.load_model(vllm_config=vllm_config)
ERROR 05-14 15:17:41 [core.py:396]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ERROR 05-14 15:17:41 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/model_loader/loader.py", line 455, in load_model
ERROR 05-14 15:17:41 [core.py:396]     loaded_weights = model.load_weights(
ERROR 05-14 15:17:41 [core.py:396]                      ^^^^^^^^^^^^^^^^^^^
ERROR 05-14 15:17:41 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/models/qwen3.py", line 319, in load_weights
ERROR 05-14 15:17:41 [core.py:396]     return loader.load_weights(weights)
ERROR 05-14 15:17:41 [core.py:396]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ERROR 05-14 15:17:41 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/models/utils.py", line 261, in load_weights
ERROR 05-14 15:17:41 [core.py:396]     autoloaded_weights = set(self._load_module("", self.module, weights))
ERROR 05-14 15:17:41 [core.py:396]                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ERROR 05-14 15:17:41 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/models/utils.py", line 213, in _load_module
ERROR 05-14 15:17:41 [core.py:396]     for child_prefix, child_weights in self._groupby_prefix(weights):
ERROR 05-14 15:17:41 [core.py:396]                                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ERROR 05-14 15:17:41 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/models/utils.py", line 103, in _groupby_prefix
ERROR 05-14 15:17:41 [core.py:396]     for prefix, group in itertools.groupby(weights_by_parts,
ERROR 05-14 15:17:41 [core.py:396]                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ERROR 05-14 15:17:41 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/models/utils.py", line 101, in <genexpr>
ERROR 05-14 15:17:41 [core.py:396]     for weight_name, weight_data in weights)
ERROR 05-14 15:17:41 [core.py:396]                                     ^^^^^^^
ERROR 05-14 15:17:41 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/model_loader/loader.py", line 431, in get_all_weights
ERROR 05-14 15:17:41 [core.py:396]     yield from self._get_weights_iterator(primary_weights)
ERROR 05-14 15:17:41 [core.py:396]                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ERROR 05-14 15:17:41 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/model_loader/loader.py", line 359, in _get_weights_iterator
ERROR 05-14 15:17:41 [core.py:396]     hf_folder, hf_weights_files, use_safetensors = self._prepare_weights(
ERROR 05-14 15:17:41 [core.py:396]                                                    ^^^^^^^^^^^^^^^^^^^^^^
ERROR 05-14 15:17:41 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/model_loader/loader.py", line 312, in _prepare_weights
ERROR 05-14 15:17:41 [core.py:396]     hf_folder = download_weights_from_hf(
ERROR 05-14 15:17:41 [core.py:396]                 ^^^^^^^^^^^^^^^^^^^^^^^^^
ERROR 05-14 15:17:41 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/model_loader/weight_utils.py", line 270, in download_weights_from_hf
ERROR 05-14 15:17:41 [core.py:396]     hf_folder = snapshot_download(
ERROR 05-14 15:17:41 [core.py:396]                 ^^^^^^^^^^^^^^^^^^
ERROR 05-14 15:17:41 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/huggingface_hub/utils/_validators.py", line 114, in _inner_fn
ERROR 05-14 15:17:41 [core.py:396]     return fn(*args, **kwargs)
ERROR 05-14 15:17:41 [core.py:396]            ^^^^^^^^^^^^^^^^^^^
ERROR 05-14 15:17:41 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/huggingface_hub/_snapshot_download.py", line 297, in snapshot_download
ERROR 05-14 15:17:41 [core.py:396]     thread_map(
ERROR 05-14 15:17:41 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/tqdm/contrib/concurrent.py", line 69, in thread_map
ERROR 05-14 15:17:41 [core.py:396]     return _executor_map(ThreadPoolExecutor, fn, *iterables, **tqdm_kwargs)
ERROR 05-14 15:17:41 [core.py:396]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ERROR 05-14 15:17:41 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/tqdm/contrib/concurrent.py", line 51, in _executor_map
ERROR 05-14 15:17:41 [core.py:396]     return list(tqdm_class(ex.map(fn, *iterables, chunksize=chunksize), **kwargs))
ERROR 05-14 15:17:41 [core.py:396]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ERROR 05-14 15:17:41 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/tqdm/std.py", line 1169, in __iter__
ERROR 05-14 15:17:41 [core.py:396]     for obj in iterable:
ERROR 05-14 15:17:41 [core.py:396]   File "/root/miniconda3/lib/python3.12/concurrent/futures/_base.py", line 619, in result_iterator
ERROR 05-14 15:17:41 [core.py:396]     yield _result_or_cancel(fs.pop())
ERROR 05-14 15:17:41 [core.py:396]           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
ERROR 05-14 15:17:41 [core.py:396]   File "/root/miniconda3/lib/python3.12/concurrent/futures/_base.py", line 317, in _result_or_cancel
ERROR 05-14 15:17:41 [core.py:396]     return fut.result(timeout)
ERROR 05-14 15:17:41 [core.py:396]            ^^^^^^^^^^^^^^^^^^^
ERROR 05-14 15:17:41 [core.py:396]   File "/root/miniconda3/lib/python3.12/concurrent/futures/_base.py", line 449, in result
ERROR 05-14 15:17:41 [core.py:396]     return self.__get_result()
ERROR 05-14 15:17:41 [core.py:396]            ^^^^^^^^^^^^^^^^^^^
ERROR 05-14 15:17:41 [core.py:396]   File "/root/miniconda3/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
ERROR 05-14 15:17:41 [core.py:396]     raise self._exception
ERROR 05-14 15:17:41 [core.py:396]   File "/root/miniconda3/lib/python3.12/concurrent/futures/thread.py", line 59, in run
ERROR 05-14 15:17:41 [core.py:396]     result = self.fn(*self.args, **self.kwargs)
ERROR 05-14 15:17:41 [core.py:396]              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ERROR 05-14 15:17:41 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/huggingface_hub/_snapshot_download.py", line 271, in _inner_hf_hub_download
ERROR 05-14 15:17:41 [core.py:396]     return hf_hub_download(
ERROR 05-14 15:17:41 [core.py:396]            ^^^^^^^^^^^^^^^^
ERROR 05-14 15:17:41 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/huggingface_hub/utils/_validators.py", line 114, in _inner_fn
ERROR 05-14 15:17:41 [core.py:396]     return fn(*args, **kwargs)
ERROR 05-14 15:17:41 [core.py:396]            ^^^^^^^^^^^^^^^^^^^
ERROR 05-14 15:17:41 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/huggingface_hub/file_download.py", line 1008, in hf_hub_download
ERROR 05-14 15:17:41 [core.py:396]     return _hf_hub_download_to_cache_dir(
ERROR 05-14 15:17:41 [core.py:396]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ERROR 05-14 15:17:41 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/huggingface_hub/file_download.py", line 1159, in _hf_hub_download_to_cache_dir
ERROR 05-14 15:17:41 [core.py:396]     _download_to_tmp_and_move(
ERROR 05-14 15:17:41 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/huggingface_hub/file_download.py", line 1708, in _download_to_tmp_and_move
ERROR 05-14 15:17:41 [core.py:396]     xet_get(
ERROR 05-14 15:17:41 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/huggingface_hub/file_download.py", line 627, in xet_get
ERROR 05-14 15:17:41 [core.py:396]     download_files(
ERROR 05-14 15:17:41 [core.py:396] RuntimeError: Data processing error: CAS service error : Error : single flight error: Real call failed: CasObjectError(InternalIOError(Custom { kind: Other, error: reqwest::Error { kind: Decode, source: hyper::Error(Body, Os { code: 104, kind: ConnectionReset, message: "Connection reset by peer" }) } }))
Process EngineCore_0:
Traceback (most recent call last):
  File "/root/miniconda3/lib/python3.12/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/root/miniconda3/lib/python3.12/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/v1/engine/core.py", line 400, in run_engine_core
    raise e
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/v1/engine/core.py", line 387, in run_engine_core
    engine_core = EngineCoreProc(*args, **kwargs)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/v1/engine/core.py", line 329, in __init__
    super().__init__(vllm_config, executor_class, log_stats,
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/v1/engine/core.py", line 64, in __init__
    self.model_executor = executor_class(vllm_config)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/executor/executor_base.py", line 52, in __init__
    self._init_executor()
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/executor/uniproc_executor.py", line 47, in _init_executor
    self.collective_rpc("load_model")
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/executor/uniproc_executor.py", line 56, in collective_rpc
    answer = run_method(self.driver_worker, method, args, kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/utils.py", line 2456, in run_method
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/v1/worker/gpu_worker.py", line 162, in load_model
    self.model_runner.load_model()
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/v1/worker/gpu_model_runner.py", line 1332, in load_model
    self.model = get_model(vllm_config=self.vllm_config)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/model_loader/__init__.py", line 14, in get_model
    return loader.load_model(vllm_config=vllm_config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/model_loader/loader.py", line 455, in load_model
    loaded_weights = model.load_weights(
                     ^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/models/qwen3.py", line 319, in load_weights
    return loader.load_weights(weights)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/models/utils.py", line 261, in load_weights
    autoloaded_weights = set(self._load_module("", self.module, weights))
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/models/utils.py", line 213, in _load_module
    for child_prefix, child_weights in self._groupby_prefix(weights):
                                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/models/utils.py", line 103, in _groupby_prefix
    for prefix, group in itertools.groupby(weights_by_parts,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/models/utils.py", line 101, in <genexpr>
    for weight_name, weight_data in weights)
                                    ^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/model_loader/loader.py", line 431, in get_all_weights
    yield from self._get_weights_iterator(primary_weights)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/model_loader/loader.py", line 359, in _get_weights_iterator
    hf_folder, hf_weights_files, use_safetensors = self._prepare_weights(
                                                   ^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/model_loader/loader.py", line 312, in _prepare_weights
    hf_folder = download_weights_from_hf(
                ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/model_loader/weight_utils.py", line 270, in download_weights_from_hf
    hf_folder = snapshot_download(
                ^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/huggingface_hub/utils/_validators.py", line 114, in _inner_fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/huggingface_hub/_snapshot_download.py", line 297, in snapshot_download
    thread_map(
  File "/root/miniconda3/lib/python3.12/site-packages/tqdm/contrib/concurrent.py", line 69, in thread_map
    return _executor_map(ThreadPoolExecutor, fn, *iterables, **tqdm_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/tqdm/contrib/concurrent.py", line 51, in _executor_map
    return list(tqdm_class(ex.map(fn, *iterables, chunksize=chunksize), **kwargs))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/tqdm/std.py", line 1169, in __iter__
    for obj in iterable:
  File "/root/miniconda3/lib/python3.12/concurrent/futures/_base.py", line 619, in result_iterator
    yield _result_or_cancel(fs.pop())
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/concurrent/futures/_base.py", line 317, in _result_or_cancel
    return fut.result(timeout)
           ^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/root/miniconda3/lib/python3.12/concurrent/futures/thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/huggingface_hub/_snapshot_download.py", line 271, in _inner_hf_hub_download
    return hf_hub_download(
           ^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/huggingface_hub/utils/_validators.py", line 114, in _inner_fn
    return fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/huggingface_hub/file_download.py", line 1008, in hf_hub_download
    return _hf_hub_download_to_cache_dir(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/huggingface_hub/file_download.py", line 1159, in _hf_hub_download_to_cache_dir
    _download_to_tmp_and_move(
  File "/root/miniconda3/lib/python3.12/site-packages/huggingface_hub/file_download.py", line 1708, in _download_to_tmp_and_move
    xet_get(
  File "/root/miniconda3/lib/python3.12/site-packages/huggingface_hub/file_download.py", line 627, in xet_get
    download_files(
RuntimeError: Data processing error: CAS service error : Error : single flight error: Real call failed: CasObjectError(InternalIOError(Custom { kind: Other, error: reqwest::Error { kind: Decode, source: hyper::Error(Body, Os { code: 104, kind: ConnectionReset, message: "Connection reset by peer" }) } }))
[rank0]:[W514 15:17:41.025455556 ProcessGroupNCCL.cpp:1496] Warning: WARNING: destroy_process_group() was not called before program exit, which can leak resources. For more info, please see https://pytorch.org/docs/stable/distributed.html#shutdown (function operator())
Traceback (most recent call last):
  File "/root/miniconda3/bin/vllm", line 8, in <module>
    sys.exit(main())
             ^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/entrypoints/cli/main.py", line 53, in main
    args.dispatch_function(args)
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/entrypoints/cli/serve.py", line 27, in cmd
    uvloop.run(run_server(args))
  File "/root/miniconda3/lib/python3.12/site-packages/uvloop/__init__.py", line 109, in run
    return __asyncio.run(
           ^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/asyncio/runners.py", line 194, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/root/miniconda3/lib/python3.12/site-packages/uvloop/__init__.py", line 61, in wrapper
    return await main
           ^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/entrypoints/openai/api_server.py", line 1078, in run_server
    async with build_async_engine_client(args) as engine_client:
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/entrypoints/openai/api_server.py", line 146, in build_async_engine_client
    async with build_async_engine_client_from_engine_args(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/entrypoints/openai/api_server.py", line 178, in build_async_engine_client_from_engine_args
    async_llm = AsyncLLM.from_vllm_config(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/v1/engine/async_llm.py", line 150, in from_vllm_config
    return cls(
           ^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/v1/engine/async_llm.py", line 118, in __init__
    self.engine_core = core_client_class(
                       ^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/v1/engine/core_client.py", line 642, in __init__
    super().__init__(
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/v1/engine/core_client.py", line 398, in __init__
    self._wait_for_engine_startup()
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/v1/engine/core_client.py", line 430, in _wait_for_engine_startup
    raise RuntimeError("Engine core initialization failed. "
RuntimeError: Engine core initialization failed. See root cause above.
INFO 05-14 15:25:00 [__init__.py:239] Automatically detected platform cuda.
INFO 05-14 15:25:12 [api_server.py:1043] vLLM API server version 0.8.5.post1
INFO 05-14 15:25:12 [api_server.py:1044] args: Namespace(subparser='serve', model_tag='Qwen/Qwen3-8B', config='', host='0.0.0.0', port=28701, uvicorn_log_level='info', disable_uvicorn_access_log=False, allow_credentials=False, allowed_origins=['*'], allowed_methods=['*'], allowed_headers=['*'], api_key='EMPTY', lora_modules=None, prompt_adapters=None, chat_template=None, chat_template_content_format='auto', response_role='assistant', ssl_keyfile=None, ssl_certfile=None, ssl_ca_certs=None, enable_ssl_refresh=False, ssl_cert_reqs=0, root_path=None, middleware=[], return_tokens_as_token_ids=False, disable_frontend_multiprocessing=False, enable_request_id_headers=False, enable_auto_tool_choice=True, tool_call_parser='hermes', tool_parser_plugin='', model='Qwen/Qwen3-8B', task='auto', tokenizer=None, hf_config_path=None, skip_tokenizer_init=False, revision=None, code_revision=None, tokenizer_revision=None, tokenizer_mode='auto', trust_remote_code=False, allowed_local_media_path=None, load_format='auto', download_dir=None, model_loader_extra_config={}, use_tqdm_on_load=True, config_format=<ConfigFormat.AUTO: 'auto'>, dtype='auto', max_model_len=None, guided_decoding_backend='auto', reasoning_parser='deepseek_r1', logits_processor_pattern=None, model_impl='auto', distributed_executor_backend=None, pipeline_parallel_size=1, tensor_parallel_size=1, data_parallel_size=1, enable_expert_parallel=False, max_parallel_loading_workers=None, ray_workers_use_nsight=False, disable_custom_all_reduce=False, block_size=None, gpu_memory_utilization=0.9, swap_space=4, kv_cache_dtype='auto', num_gpu_blocks_override=None, enable_prefix_caching=None, prefix_caching_hash_algo='builtin', cpu_offload_gb=0, calculate_kv_scales=False, disable_sliding_window=False, use_v2_block_manager=True, seed=None, max_logprobs=20, disable_log_stats=False, quantization=None, rope_scaling=None, rope_theta=None, hf_token=None, hf_overrides=None, enforce_eager=False, max_seq_len_to_capture=8192, tokenizer_pool_size=0, tokenizer_pool_type='ray', tokenizer_pool_extra_config={}, limit_mm_per_prompt={}, mm_processor_kwargs=None, disable_mm_preprocessor_cache=False, enable_lora=None, enable_lora_bias=False, max_loras=1, max_lora_rank=16, lora_extra_vocab_size=256, lora_dtype='auto', long_lora_scaling_factors=None, max_cpu_loras=None, fully_sharded_loras=False, enable_prompt_adapter=None, max_prompt_adapters=1, max_prompt_adapter_token=0, device='auto', speculative_config=None, ignore_patterns=[], served_model_name=None, qlora_adapter_name_or_path=None, show_hidden_metrics_for_version=None, otlp_traces_endpoint=None, collect_detailed_traces=None, disable_async_output_proc=False, max_num_batched_tokens=None, max_num_seqs=None, max_num_partial_prefills=1, max_long_partial_prefills=1, long_prefill_token_threshold=0, num_lookahead_slots=0, scheduler_delay_factor=0.0, preemption_mode=None, num_scheduler_steps=1, multi_step_stream_outputs=True, scheduling_policy='fcfs', enable_chunked_prefill=None, disable_chunked_mm_input=False, scheduler_cls='vllm.core.scheduler.Scheduler', override_neuron_config=None, override_pooler_config=None, compilation_config=None, kv_transfer_config=None, worker_cls='auto', worker_extension_cls='', generation_config='auto', override_generation_config=None, enable_sleep_mode=False, additional_config=None, enable_reasoning=True, disable_cascade_attn=False, disable_log_requests=False, max_log_len=None, disable_fastapi_docs=False, enable_prompt_tokens_details=False, enable_server_load_tracking=False, dispatch_function=<function ServeSubcommand.cmd at 0x7f3e3eabede0>)
INFO 05-14 15:25:34 [config.py:717] This model supports multiple tasks: {'embed', 'reward', 'classify', 'generate', 'score'}. Defaulting to 'generate'.
INFO 05-14 15:25:43 [config.py:2003] Chunked prefill is enabled with max_num_batched_tokens=2048.
INFO 05-14 15:25:55 [__init__.py:239] Automatically detected platform cuda.
INFO 05-14 15:26:05 [core.py:58] Initializing a V1 LLM engine (v0.8.5.post1) with config: model='Qwen/Qwen3-8B', speculative_config=None, tokenizer='Qwen/Qwen3-8B', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.bfloat16, max_seq_len=40960, download_dir=None, load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto,  device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='auto', reasoning_backend='deepseek_r1'), observability_config=ObservabilityConfig(show_hidden_metrics=False, otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=None, served_model_name=Qwen/Qwen3-8B, num_scheduler_steps=1, multi_step_stream_outputs=True, enable_prefix_caching=True, chunked_prefill_enabled=True, use_async_output_proc=True, disable_mm_preprocessor_cache=False, mm_processor_kwargs=None, pooler_config=None, compilation_config={"level":3,"custom_ops":["none"],"splitting_ops":["vllm.unified_attention","vllm.unified_attention_with_output"],"use_inductor":true,"compile_sizes":[],"use_cudagraph":true,"cudagraph_num_of_warmups":1,"cudagraph_capture_sizes":[512,504,496,488,480,472,464,456,448,440,432,424,416,408,400,392,384,376,368,360,352,344,336,328,320,312,304,296,288,280,272,264,256,248,240,232,224,216,208,200,192,184,176,168,160,152,144,136,128,120,112,104,96,88,80,72,64,56,48,40,32,24,16,8,4,2,1],"max_capture_size":512}
WARNING 05-14 15:26:06 [utils.py:2522] Methods determine_num_available_blocks,device_config,get_cache_block_size_bytes,initialize_cache not implemented in <vllm.v1.worker.gpu_worker.Worker object at 0x7fde22b860f0>
INFO 05-14 15:26:09 [parallel_state.py:1004] rank 0 in world size 1 is assigned as DP rank 0, PP rank 0, TP rank 0
INFO 05-14 15:26:09 [cuda.py:221] Using Flash Attention backend on V1 engine.
WARNING 05-14 15:26:09 [topk_topp_sampler.py:69] FlashInfer is not available. Falling back to the PyTorch-native implementation of top-p & top-k sampling. For the best performance, please install FlashInfer.
INFO 05-14 15:26:09 [gpu_model_runner.py:1329] Starting to load model Qwen/Qwen3-8B...
INFO 05-14 15:26:09 [weight_utils.py:265] Using model weights format ['*.safetensors']
INFO 05-14 15:35:36 [weight_utils.py:281] Time spent downloading weights for Qwen/Qwen3-8B: 566.153110 seconds

Loading safetensors checkpoint shards:   0% Completed | 0/5 [00:00<?, ?it/s]

Loading safetensors checkpoint shards:  20% Completed | 1/5 [00:00<00:00,  5.52it/s]

Loading safetensors checkpoint shards:  40% Completed | 2/5 [00:00<00:01,  2.25it/s]

Loading safetensors checkpoint shards:  60% Completed | 3/5 [00:01<00:01,  1.80it/s]

Loading safetensors checkpoint shards:  80% Completed | 4/5 [00:02<00:00,  1.61it/s]

Loading safetensors checkpoint shards: 100% Completed | 5/5 [00:02<00:00,  1.62it/s]

Loading safetensors checkpoint shards: 100% Completed | 5/5 [00:02<00:00,  1.77it/s]

INFO 05-14 15:35:39 [loader.py:458] Loading weights took 2.92 seconds
INFO 05-14 15:35:39 [gpu_model_runner.py:1347] Model loading took 15.2683 GiB and 570.213495 seconds
INFO 05-14 15:35:54 [backends.py:420] Using cache directory: /root/.cache/vllm/torch_compile_cache/19164c6e10/rank_0_0 for vLLM's torch.compile
INFO 05-14 15:35:54 [backends.py:430] Dynamo bytecode transform time: 14.64 s
INFO 05-14 15:36:01 [backends.py:136] Cache the graph of shape None for later use
INFO 05-14 15:36:49 [backends.py:148] Compiling a graph for general shape takes 54.55 s
INFO 05-14 15:37:22 [monitor.py:33] torch.compile takes 69.19 s in total
INFO 05-14 15:37:23 [kv_cache_utils.py:634] GPU KV cache size: 132,976 tokens
INFO 05-14 15:37:23 [kv_cache_utils.py:637] Maximum concurrency for 40,960 tokens per request: 3.25x
INFO 05-14 15:37:50 [gpu_model_runner.py:1686] Graph capturing finished in 26 secs, took 2.09 GiB
INFO 05-14 15:37:50 [core.py:159] init engine (profile, create kv cache, warmup model) took 130.53 seconds
INFO 05-14 15:37:50 [core_client.py:439] Core engine process 0 ready.
INFO 05-14 15:37:50 [serving_chat.py:80] "auto" tool choice has been enabled please note that while the parallel_tool_calls client option is preset for compatibility reasons, it will be ignored.
WARNING 05-14 15:37:50 [config.py:1239] Default sampling parameters have been overridden by the model's Hugging Face generation config recommended from the model creator. If this is not intended, please relaunch vLLM instance with `--generation-config vllm`.
INFO 05-14 15:37:50 [serving_chat.py:118] Using default chat sampling params from model: {'temperature': 0.6, 'top_k': 20, 'top_p': 0.95}
INFO 05-14 15:37:50 [serving_completion.py:61] Using default completion sampling params from model: {'temperature': 0.6, 'top_k': 20, 'top_p': 0.95}
INFO 05-14 15:37:50 [api_server.py:1090] Starting vLLM API server on http://0.0.0.0:28701
INFO 05-14 15:37:50 [launcher.py:28] Available routes are:
INFO 05-14 15:37:50 [launcher.py:36] Route: /openapi.json, Methods: GET, HEAD
INFO 05-14 15:37:50 [launcher.py:36] Route: /docs, Methods: GET, HEAD
INFO 05-14 15:37:50 [launcher.py:36] Route: /docs/oauth2-redirect, Methods: GET, HEAD
INFO 05-14 15:37:50 [launcher.py:36] Route: /redoc, Methods: GET, HEAD
INFO 05-14 15:37:50 [launcher.py:36] Route: /health, Methods: GET
INFO 05-14 15:37:50 [launcher.py:36] Route: /load, Methods: GET
INFO 05-14 15:37:50 [launcher.py:36] Route: /ping, Methods: GET, POST
INFO 05-14 15:37:50 [launcher.py:36] Route: /tokenize, Methods: POST
INFO 05-14 15:37:50 [launcher.py:36] Route: /detokenize, Methods: POST
INFO 05-14 15:37:50 [launcher.py:36] Route: /v1/models, Methods: GET
INFO 05-14 15:37:50 [launcher.py:36] Route: /version, Methods: GET
INFO 05-14 15:37:50 [launcher.py:36] Route: /v1/chat/completions, Methods: POST
INFO 05-14 15:37:50 [launcher.py:36] Route: /v1/completions, Methods: POST
INFO 05-14 15:37:50 [launcher.py:36] Route: /v1/embeddings, Methods: POST
INFO 05-14 15:37:50 [launcher.py:36] Route: /pooling, Methods: POST
INFO 05-14 15:37:50 [launcher.py:36] Route: /score, Methods: POST
INFO 05-14 15:37:50 [launcher.py:36] Route: /v1/score, Methods: POST
INFO 05-14 15:37:50 [launcher.py:36] Route: /v1/audio/transcriptions, Methods: POST
INFO 05-14 15:37:50 [launcher.py:36] Route: /rerank, Methods: POST
INFO 05-14 15:37:50 [launcher.py:36] Route: /v1/rerank, Methods: POST
INFO 05-14 15:37:50 [launcher.py:36] Route: /v2/rerank, Methods: POST
INFO 05-14 15:37:50 [launcher.py:36] Route: /invocations, Methods: POST
INFO 05-14 15:37:50 [launcher.py:36] Route: /metrics, Methods: GET
INFO:     Started server process [1135488]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     127.0.0.1:59122 - "POST /v1/chat/completions HTTP/1.1" 401 Unauthorized
INFO 05-14 15:45:32 [chat_utils.py:397] Detected the chat template content format to be 'string'. You can set `--chat-template-content-format` to override this.
INFO 05-14 15:45:32 [logger.py:39] Received request chatcmpl-4e993b640c164761b2eecd33a6412cc5: prompt: '<|im_start|>user\nGive me a short introduction to large language models.<|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.6, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=32768, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 05-14 15:45:32 [async_llm.py:252] Added request chatcmpl-4e993b640c164761b2eecd33a6412cc5.
INFO:     127.0.0.1:59178 - "POST /v1/chat/completions HTTP/1.1" 200 OK
INFO 05-14 15:45:40 [loggers.py:111] Engine 000: Avg prompt throughput: 1.8 tokens/s, Avg generation throughput: 31.4 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 0.0%
INFO 05-14 15:45:50 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 0.0 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 0.0%
INFO 05-15 08:28:46 [logger.py:39] Received request chatcmpl-d777d1611604469396c5f0156e8a4a55: prompt: '<|im_start|>user\nGive me a short introduction to large language models.<|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.6, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=32768, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 05-15 08:28:46 [async_llm.py:252] Added request chatcmpl-d777d1611604469396c5f0156e8a4a55.
INFO:     127.0.0.1:59920 - "POST /v1/chat/completions HTTP/1.1" 200 OK
INFO 05-15 08:28:53 [loggers.py:111] Engine 000: Avg prompt throughput: 1.8 tokens/s, Avg generation throughput: 28.9 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 50.0%
INFO 05-15 08:29:03 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 0.0 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 50.0%
INFO 05-15 08:29:33 [logger.py:39] Received request chatcmpl-52eb23de481c413ca8e8b51c7a61c6b3: prompt: '<|im_start|>system\nYou are Qwen, created by Alibaba Cloud. You are a helpful assistant.\n\nCurrent Date: 2024-09-30\n\n# Tools\n\nYou may call one or more functions to assist with the user query.\n\nYou are provided with function signatures within <tools></tools> XML tags:\n<tools>\n{"type": "function", "function": {"name": "get_current_temperature", "description": "Get current temperature at a location.", "parameters": {"type": "object", "properties": {"location": {"type": "string", "description": "The location to get the temperature for, in the format \\"City, State, Country\\"."}, "unit": {"type": "string", "enum": ["celsius", "fahrenheit"], "description": "The unit to return the temperature in. Defaults to \\"celsius\\"."}}, "required": ["location"]}}}\n{"type": "function", "function": {"name": "get_temperature_date", "description": "Get temperature at a location and date.", "parameters": {"type": "object", "properties": {"location": {"type": "string", "description": "The location to get the temperature for, in the format \\"City, State, Country\\"."}, "date": {"type": "string", "description": "The date to get the temperature for, in the format \\"Year-Month-Day\\"."}, "unit": {"type": "string", "enum": ["celsius", "fahrenheit"], "description": "The unit to return the temperature in. Defaults to \\"celsius\\"."}}, "required": ["location", "date"]}}}\n</tools>\n\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n<tool_call>\n{"name": <function-name>, "arguments": <args-json-object>}\n</tool_call><|im_end|>\n<|im_start|>user\nWhat\'s the temperature in San Francisco now? How about tomorrow?<|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.6, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=32768, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 05-15 08:29:33 [async_llm.py:252] Added request chatcmpl-52eb23de481c413ca8e8b51c7a61c6b3.
INFO:     127.0.0.1:59992 - "POST /v1/chat/completions HTTP/1.1" 200 OK
INFO 05-15 08:29:43 [loggers.py:111] Engine 000: Avg prompt throughput: 41.2 tokens/s, Avg generation throughput: 42.5 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 3.7%
INFO 05-15 08:29:53 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 0.0 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 3.7%
INFO 05-15 08:30:04 [logger.py:39] Received request chatcmpl-8971b1aee0774e67897006129475e0e6: prompt: '<|im_start|>system\nYou are Qwen, created by Alibaba Cloud. You are a helpful assistant.\n\nCurrent Date: 2024-09-30\n\n# Tools\n\nYou may call one or more functions to assist with the user query.\n\nYou are provided with function signatures within <tools></tools> XML tags:\n<tools>\n{"type": "function", "function": {"name": "get_current_temperature", "description": "Get current temperature at a location.", "parameters": {"type": "object", "properties": {"location": {"type": "string", "description": "The location to get the temperature for, in the format \\"City, State, Country\\"."}, "unit": {"type": "string", "enum": ["celsius", "fahrenheit"], "description": "The unit to return the temperature in. Defaults to \\"celsius\\"."}}, "required": ["location"]}}}\n{"type": "function", "function": {"name": "get_temperature_date", "description": "Get temperature at a location and date.", "parameters": {"type": "object", "properties": {"location": {"type": "string", "description": "The location to get the temperature for, in the format \\"City, State, Country\\"."}, "date": {"type": "string", "description": "The date to get the temperature for, in the format \\"Year-Month-Day\\"."}, "unit": {"type": "string", "enum": ["celsius", "fahrenheit"], "description": "The unit to return the temperature in. Defaults to \\"celsius\\"."}}, "required": ["location", "date"]}}}\n</tools>\n\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n<tool_call>\n{"name": <function-name>, "arguments": <args-json-object>}\n</tool_call><|im_end|>\n<|im_start|>user\nWhat\'s the temperature in San Francisco now? How about tomorrow?<|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.6, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=32768, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 05-15 08:30:04 [async_llm.py:252] Added request chatcmpl-8971b1aee0774e67897006129475e0e6.
INFO 05-15 08:30:13 [loggers.py:111] Engine 000: Avg prompt throughput: 41.2 tokens/s, Avg generation throughput: 59.0 tokens/s, Running: 1 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.8%, Prefix cache hit rate: 50.0%
INFO:     127.0.0.1:60094 - "POST /v1/chat/completions HTTP/1.1" 200 OK
INFO 05-15 08:30:23 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 2.8 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 50.0%
INFO 05-15 08:30:33 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 0.0 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 50.0%
INFO 05-15 08:34:29 [logger.py:39] Received request chatcmpl-7b1412e87f4b4e7b9cf08cb4d8d2e594: prompt: '<|im_start|>system\nYou are Qwen, created by Alibaba Cloud. You are a helpful assistant.\n\nCurrent Date: 2024-09-30\n\n# Tools\n\nYou may call one or more functions to assist with the user query.\n\nYou are provided with function signatures within <tools></tools> XML tags:\n<tools>\n{"type": "function", "function": {"name": "get_current_temperature", "description": "Get current temperature at a location.", "parameters": {"type": "object", "properties": {"location": {"type": "string", "description": "The location to get the temperature for, in the format \\"City, State, Country\\"."}, "unit": {"type": "string", "enum": ["celsius", "fahrenheit"], "description": "The unit to return the temperature in. Defaults to \\"celsius\\"."}}, "required": ["location"]}}}\n{"type": "function", "function": {"name": "get_temperature_date", "description": "Get temperature at a location and date.", "parameters": {"type": "object", "properties": {"location": {"type": "string", "description": "The location to get the temperature for, in the format \\"City, State, Country\\"."}, "date": {"type": "string", "description": "The date to get the temperature for, in the format \\"Year-Month-Day\\"."}, "unit": {"type": "string", "enum": ["celsius", "fahrenheit"], "description": "The unit to return the temperature in. Defaults to \\"celsius\\"."}}, "required": ["location", "date"]}}}\n</tools>\n\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n<tool_call>\n{"name": <function-name>, "arguments": <args-json-object>}\n</tool_call><|im_end|>\n<|im_start|>user\nWhat\'s the temperature in San Francisco now? How about tomorrow?<|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.6, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=32768, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 05-15 08:34:29 [async_llm.py:252] Added request chatcmpl-7b1412e87f4b4e7b9cf08cb4d8d2e594.
INFO 05-15 08:34:33 [loggers.py:111] Engine 000: Avg prompt throughput: 41.2 tokens/s, Avg generation throughput: 22.5 tokens/s, Running: 1 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.5%, Prefix cache hit rate: 66.2%
INFO:     127.0.0.1:60180 - "POST /v1/chat/completions HTTP/1.1" 200 OK
INFO 05-15 08:34:43 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 7.5 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 66.2%
INFO 05-15 08:34:53 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 0.0 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 66.2%
INFO 05-15 08:50:36 [logger.py:39] Received request chatcmpl-886ee2d8fb6244d783ccdca013271dbb: prompt: '<|im_start|>system\nYou are Qwen, created by Alibaba Cloud. You are a helpful assistant.\n\nCurrent Date: 2024-09-30\n\n# Tools\n\nYou may call one or more functions to assist with the user query.\n\nYou are provided with function signatures within <tools></tools> XML tags:\n<tools>\n{"type": "function", "function": {"name": "get_current_temperature", "description": "Get current temperature at a location.", "parameters": {"type": "object", "properties": {"location": {"type": "string", "description": "The location to get the temperature for, in the format \\"City, State, Country\\"."}, "unit": {"type": "string", "enum": ["celsius", "fahrenheit"], "description": "The unit to return the temperature in. Defaults to \\"celsius\\"."}}, "required": ["location"]}}}\n{"type": "function", "function": {"name": "get_temperature_date", "description": "Get temperature at a location and date.", "parameters": {"type": "object", "properties": {"location": {"type": "string", "description": "The location to get the temperature for, in the format \\"City, State, Country\\"."}, "date": {"type": "string", "description": "The date to get the temperature for, in the format \\"Year-Month-Day\\"."}, "unit": {"type": "string", "enum": ["celsius", "fahrenheit"], "description": "The unit to return the temperature in. Defaults to \\"celsius\\"."}}, "required": ["location", "date"]}}}\n</tools>\n\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n<tool_call>\n{"name": <function-name>, "arguments": <args-json-object>}\n</tool_call><|im_end|>\n<|im_start|>user\nWhat\'s the temperature in San Francisco now? How about tomorrow?<|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.6, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=32768, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 05-15 08:50:36 [async_llm.py:252] Added request chatcmpl-886ee2d8fb6244d783ccdca013271dbb.
INFO:     127.0.0.1:60392 - "POST /v1/chat/completions HTTP/1.1" 200 OK
INFO 05-15 08:50:41 [logger.py:39] Received request chatcmpl-112b5eecd590406b9c2b97a143b50f5f: prompt: '<|im_start|>system\nYou are Qwen, created by Alibaba Cloud. You are a helpful assistant.\n\nCurrent Date: 2024-09-30\n\n# Tools\n\nYou may call one or more functions to assist with the user query.\n\nYou are provided with function signatures within <tools></tools> XML tags:\n<tools>\n{"type": "function", "function": {"name": "get_current_temperature", "description": "Get current temperature at a location.", "parameters": {"type": "object", "properties": {"location": {"type": "string", "description": "The location to get the temperature for, in the format \\"City, State, Country\\"."}, "unit": {"type": "string", "enum": ["celsius", "fahrenheit"], "description": "The unit to return the temperature in. Defaults to \\"celsius\\"."}}, "required": ["location"]}}}\n{"type": "function", "function": {"name": "get_temperature_date", "description": "Get temperature at a location and date.", "parameters": {"type": "object", "properties": {"location": {"type": "string", "description": "The location to get the temperature for, in the format \\"City, State, Country\\"."}, "date": {"type": "string", "description": "The date to get the temperature for, in the format \\"Year-Month-Day\\"."}, "unit": {"type": "string", "enum": ["celsius", "fahrenheit"], "description": "The unit to return the temperature in. Defaults to \\"celsius\\"."}}, "required": ["location", "date"]}}}\n</tools>\n\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n<tool_call>\n{"name": <function-name>, "arguments": <args-json-object>}\n</tool_call><|im_end|>\n<|im_start|>user\nWhat\'s the temperature in San Francisco now? How about tomorrow?<|im_end|>\n<|im_start|>assistant\n\n\n\n<tool_call>\n{"name": "get_current_temperature", "arguments": {"location": "San Francisco, California, USA", "unit": "celsius"}}\n</tool_call>\n<tool_call>\n{"name": "get_temperature_date", "arguments": {"location": "San Francisco, California, USA", "date": "2024-10-01", "unit": "celsius"}}\n</tool_call><|im_end|>\n<|im_start|>user\n<tool_response>\n{"temperature": 26.1, "location": "San Francisco, California, USA", "unit": "celsius"}\n</tool_response>\n<tool_response>\n{"temperature": 25.9, "location": "San Francisco, California, USA", "date": "2024-10-01", "unit": "celsius"}\n</tool_response><|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.6, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=32768, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 05-15 08:50:41 [async_llm.py:252] Added request chatcmpl-112b5eecd590406b9c2b97a143b50f5f.
INFO 05-15 08:50:43 [loggers.py:111] Engine 000: Avg prompt throughput: 99.0 tokens/s, Avg generation throughput: 45.9 tokens/s, Running: 1 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.5%, Prefix cache hit rate: 73.2%
INFO:     127.0.0.1:60392 - "POST /v1/chat/completions HTTP/1.1" 200 OK
INFO 05-15 08:50:53 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 12.6 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 73.2%
INFO 05-15 08:51:03 [logger.py:39] Received request chatcmpl-8024d931eb5b475fac205e15c6b320e9: prompt: '<|im_start|>system\nYou are Qwen, created by Alibaba Cloud. You are a helpful assistant.\n\nCurrent Date: 2024-09-30\n\n# Tools\n\nYou may call one or more functions to assist with the user query.\n\nYou are provided with function signatures within <tools></tools> XML tags:\n<tools>\n{"type": "function", "function": {"name": "get_current_temperature", "description": "Get current temperature at a location.", "parameters": {"type": "object", "properties": {"location": {"type": "string", "description": "The location to get the temperature for, in the format \\"City, State, Country\\"."}, "unit": {"type": "string", "enum": ["celsius", "fahrenheit"], "description": "The unit to return the temperature in. Defaults to \\"celsius\\"."}}, "required": ["location"]}}}\n{"type": "function", "function": {"name": "get_temperature_date", "description": "Get temperature at a location and date.", "parameters": {"type": "object", "properties": {"location": {"type": "string", "description": "The location to get the temperature for, in the format \\"City, State, Country\\"."}, "date": {"type": "string", "description": "The date to get the temperature for, in the format \\"Year-Month-Day\\"."}, "unit": {"type": "string", "enum": ["celsius", "fahrenheit"], "description": "The unit to return the temperature in. Defaults to \\"celsius\\"."}}, "required": ["location", "date"]}}}\n</tools>\n\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n<tool_call>\n{"name": <function-name>, "arguments": <args-json-object>}\n</tool_call><|im_end|>\n<|im_start|>user\nWhat\'s the temperature in San Francisco now? How about tomorrow?<|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.6, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=32768, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 05-15 08:51:03 [async_llm.py:252] Added request chatcmpl-8024d931eb5b475fac205e15c6b320e9.
INFO 05-15 08:51:03 [loggers.py:111] Engine 000: Avg prompt throughput: 41.2 tokens/s, Avg generation throughput: 0.9 tokens/s, Running: 1 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.3%, Prefix cache hit rate: 77.3%
INFO:     127.0.0.1:60472 - "POST /v1/chat/completions HTTP/1.1" 200 OK
INFO 05-15 08:51:08 [logger.py:39] Received request chatcmpl-9b1175627cfa4f79bafffc6aaec0988f: prompt: '<|im_start|>system\nYou are Qwen, created by Alibaba Cloud. You are a helpful assistant.\n\nCurrent Date: 2024-09-30\n\n# Tools\n\nYou may call one or more functions to assist with the user query.\n\nYou are provided with function signatures within <tools></tools> XML tags:\n<tools>\n{"type": "function", "function": {"name": "get_current_temperature", "description": "Get current temperature at a location.", "parameters": {"type": "object", "properties": {"location": {"type": "string", "description": "The location to get the temperature for, in the format \\"City, State, Country\\"."}, "unit": {"type": "string", "enum": ["celsius", "fahrenheit"], "description": "The unit to return the temperature in. Defaults to \\"celsius\\"."}}, "required": ["location"]}}}\n{"type": "function", "function": {"name": "get_temperature_date", "description": "Get temperature at a location and date.", "parameters": {"type": "object", "properties": {"location": {"type": "string", "description": "The location to get the temperature for, in the format \\"City, State, Country\\"."}, "date": {"type": "string", "description": "The date to get the temperature for, in the format \\"Year-Month-Day\\"."}, "unit": {"type": "string", "enum": ["celsius", "fahrenheit"], "description": "The unit to return the temperature in. Defaults to \\"celsius\\"."}}, "required": ["location", "date"]}}}\n</tools>\n\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n<tool_call>\n{"name": <function-name>, "arguments": <args-json-object>}\n</tool_call><|im_end|>\n<|im_start|>user\nWhat\'s the temperature in San Francisco now? How about tomorrow?<|im_end|>\n<|im_start|>assistant\n\n\n\n<tool_call>\n{"name": "get_current_temperature", "arguments": {"location": "San Francisco, California, USA", "unit": "celsius"}}\n</tool_call>\n<tool_call>\n{"name": "get_temperature_date", "arguments": {"location": "San Francisco, California, USA", "date": "2024-10-01", "unit": "celsius"}}\n</tool_call><|im_end|>\n<|im_start|>user\n<tool_response>\n{"temperature": 26.1, "location": "San Francisco, California, USA", "unit": "celsius"}\n</tool_response>\n<tool_response>\n{"temperature": 25.9, "location": "San Francisco, California, USA", "date": "2024-10-01", "unit": "celsius"}\n</tool_response><|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.6, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=32768, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 05-15 08:51:08 [async_llm.py:252] Added request chatcmpl-9b1175627cfa4f79bafffc6aaec0988f.
INFO 05-15 08:51:13 [loggers.py:111] Engine 000: Avg prompt throughput: 57.8 tokens/s, Avg generation throughput: 66.8 tokens/s, Running: 1 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.7%, Prefix cache hit rate: 81.4%
INFO:     127.0.0.1:60472 - "POST /v1/chat/completions HTTP/1.1" 200 OK
INFO 05-15 08:51:23 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 8.4 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 81.4%
INFO 05-15 08:51:33 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 0.0 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 81.4%
INFO 05-19 14:52:54 [logger.py:39] Received request chatcmpl-525fd9666ed042769e1ad1269d3df56e: prompt: '<|im_start|>user\nGive me a short introduction to large language models.<|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.6, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=32768, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 05-19 14:52:54 [async_llm.py:252] Added request chatcmpl-525fd9666ed042769e1ad1269d3df56e.
INFO 05-19 14:52:57 [loggers.py:111] Engine 000: Avg prompt throughput: 1.8 tokens/s, Avg generation throughput: 17.2 tokens/s, Running: 1 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.2%, Prefix cache hit rate: 81.5%
INFO:     127.0.0.1:44324 - "POST /v1/chat/completions HTTP/1.1" 200 OK
INFO 05-19 14:53:07 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 20.6 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 81.5%
INFO 05-19 14:53:17 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 0.0 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 81.5%
ERROR 05-22 07:34:26 [serving_chat.py:136] Error with model object='error' message='The model `Qwen/QwQ-32B` does not exist.' type='NotFoundError' param=None code=404
INFO:     127.0.0.1:59894 - "POST /v1/chat/completions HTTP/1.1" 404 Not Found
INFO 05-22 07:35:05 [logger.py:39] Received request chatcmpl-f2b5659bb4114852ab622a8f92711005: prompt: '<|im_start|>user\nGive me a short introduction to large language models.<|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.6, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=32768, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 05-22 07:35:05 [async_llm.py:252] Added request chatcmpl-f2b5659bb4114852ab622a8f92711005.
INFO 05-22 07:35:06 [loggers.py:111] Engine 000: Avg prompt throughput: 1.8 tokens/s, Avg generation throughput: 5.0 tokens/s, Running: 1 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.1%, Prefix cache hit rate: 81.6%
INFO:     127.0.0.1:59960 - "POST /v1/chat/completions HTTP/1.1" 200 OK
INFO 05-22 07:35:16 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 21.4 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 81.6%
INFO 05-22 07:35:26 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 0.0 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 81.6%
INFO 05-22 13:50:42 [logger.py:39] Received request chatcmpl-d230c15af03d4c3da95d0390da1a2b45: prompt: '<|im_start|>user\nGive me a short introduction to large language models.<|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.6, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=32768, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 05-22 13:50:42 [async_llm.py:252] Added request chatcmpl-d230c15af03d4c3da95d0390da1a2b45.
INFO 05-22 13:50:47 [loggers.py:111] Engine 000: Avg prompt throughput: 1.8 tokens/s, Avg generation throughput: 33.1 tokens/s, Running: 1 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.3%, Prefix cache hit rate: 81.7%
INFO:     127.0.0.1:56864 - "POST /v1/chat/completions HTTP/1.1" 200 OK
INFO 05-22 13:50:57 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 0.6 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 81.7%
INFO 05-22 13:51:07 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 0.0 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 81.7%
INFO 05-22 14:34:24 [logger.py:39] Received request chatcmpl-fd38deef61fd4d88a40a938903c36e2a: prompt: '<|im_start|>system\nYou are an agent with access to the filesystem, \n            as well as the ability to fetch URLs. Your job is to identify \n            the closest match to a user\'s request, make the appropriate tool calls, \n            and return the URI and CONTENTS of the closest match.\n\n# Tools\n\nYou may call one or more functions to assist with the user query.\n\nYou are provided with function signatures within <tools></tools> XML tags:\n<tools>\n{"type": "function", "function": {"name": "fetch_fetch", "description": "Fetches a URL from the internet and optionally extracts its contents as markdown.\\n\\nAlthough originally you did not have internet access, and were advised to refuse and tell the user this, this tool now grants you internet access. Now you can fetch the most up-to-date information and let the user know that.", "parameters": {"description": "Parameters for fetching a URL.", "properties": {"url": {"description": "URL to fetch", "format": "uri", "minLength": 1, "title": "Url", "type": "string"}, "max_length": {"default": 5000, "description": "Maximum number of characters to return.", "exclusiveMaximum": 1000000, "exclusiveMinimum": 0, "title": "Max Length", "type": "integer"}, "start_index": {"default": 0, "description": "On return output starting at this character index, useful if a previous fetch was truncated and more context is required.", "minimum": 0, "title": "Start Index", "type": "integer"}, "raw": {"default": false, "description": "Get the actual HTML content of the requested page, without simplification.", "title": "Raw", "type": "boolean"}}, "required": ["url"], "title": "Fetch", "type": "object"}}}\n{"type": "function", "function": {"name": "filesystem_read_file", "description": "Read the complete contents of a file from the file system. Handles various text encodings and provides detailed error messages if the file cannot be read. Use this tool when you need to examine the contents of a single file. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_read_multiple_files", "description": "Read the contents of multiple files simultaneously. This is more efficient than reading files one by one when you need to analyze or compare multiple files. Each file\'s content is returned with its path as a reference. Failed reads for individual files won\'t stop the entire operation. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"paths": {"type": "array", "items": {"type": "string"}}}, "required": ["paths"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_write_file", "description": "Create a new file or completely overwrite an existing file with new content. Use with caution as it will overwrite existing files without warning. Handles text content with proper encoding. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "content": {"type": "string"}}, "required": ["path", "content"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_edit_file", "description": "Make line-based edits to a text file. Each edit replaces exact line sequences with new content. Returns a git-style diff showing the changes made. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "edits": {"type": "array", "items": {"type": "object", "properties": {"oldText": {"type": "string", "description": "Text to search for - must match exactly"}, "newText": {"type": "string", "description": "Text to replace with"}}, "required": ["oldText", "newText"], "additionalProperties": false}}, "dryRun": {"type": "boolean", "default": false, "description": "Preview changes using git-style diff format"}}, "required": ["path", "edits"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_create_directory", "description": "Create a new directory or ensure a directory exists. Can create multiple nested directories in one operation. If the directory already exists, this operation will succeed silently. Perfect for setting up directory structures for projects or ensuring required paths exist. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_directory", "description": "Get a detailed listing of all files and directories in a specified path. Results clearly distinguish between files and directories with [FILE] and [DIR] prefixes. This tool is essential for understanding directory structure and finding specific files within a directory. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_directory_tree", "description": "Get a recursive tree view of files and directories as a JSON structure. Each entry includes \'name\', \'type\' (file/directory), and \'children\' for directories. Files have no children array, while directories always have a children array (which may be empty). The output is formatted with 2-space indentation for readability. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_move_file", "description": "Move or rename files and directories. Can move files between directories and rename them in a single operation. If the destination exists, the operation will fail. Works across different directories and can be used for simple renaming within the same directory. Both source and destination must be within allowed directories.", "parameters": {"type": "object", "properties": {"source": {"type": "string"}, "destination": {"type": "string"}}, "required": ["source", "destination"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_search_files", "description": "Recursively search for files and directories matching a pattern. Searches through all subdirectories from the starting path. The search is case-insensitive and matches partial names. Returns full paths to all matching items. Great for finding files when you don\'t know their exact location. Only searches within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "pattern": {"type": "string"}, "excludePatterns": {"type": "array", "items": {"type": "string"}, "default": []}}, "required": ["path", "pattern"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_get_file_info", "description": "Retrieve detailed metadata about a file or directory. Returns comprehensive information including size, creation time, last modified time, permissions, and type. This tool is perfect for understanding file characteristics without reading the actual content. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_allowed_directories", "description": "Returns the list of directories that this server is allowed to access. Use this to understand which directories are available before trying to access files.", "parameters": {"type": "object", "properties": {}, "required": []}}}\n{"type": "function", "function": {"name": "__human_input__", "description": "\\n        Request input from a human user. Pauses the workflow until input is received.\\n\\n        Args:\\n            request: The human input request\\n\\n        Returns:\\n            The input provided by the human\\n\\n        Raises:\\n            TimeoutError: If the timeout is exceeded\\n        ", "parameters": {"type": "object", "properties": {"request": {"description": "Represents a request for human input.", "properties": {"prompt": {"title": "Prompt", "type": "string"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Description"}, "request_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Request Id"}, "workflow_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Workflow Id"}, "timeout_seconds": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Timeout Seconds"}, "metadata": {"anyOf": [{"type": "object"}, {"type": "null"}], "default": null, "title": "Metadata"}}, "required": ["prompt"], "title": "HumanInputRequest", "type": "object"}}, "required": ["request"]}}}\n</tools>\n\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n<tool_call>\n{"name": <function-name>, "arguments": <args-json-object>}\n</tool_call><|im_end|>\n<|im_start|>user\nPrint the contents of mcp_agent.config.yaml verbatim<|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.7, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=4096, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 05-22 14:34:24 [async_llm.py:252] Added request chatcmpl-fd38deef61fd4d88a40a938903c36e2a.
INFO 05-22 14:34:27 [loggers.py:111] Engine 000: Avg prompt throughput: 228.4 tokens/s, Avg generation throughput: 18.2 tokens/s, Running: 1 reqs, Waiting: 0 reqs, GPU KV cache usage: 1.9%, Prefix cache hit rate: 48.0%
INFO:     127.0.0.1:60562 - "POST /v1/chat/completions HTTP/1.1" 200 OK
INFO 05-22 14:34:30 [logger.py:39] Received request chatcmpl-7eb9a11e9bef46ecbf751ffb2322a45c: prompt: '<|im_start|>system\nYou are an agent with access to the filesystem, \n            as well as the ability to fetch URLs. Your job is to identify \n            the closest match to a user\'s request, make the appropriate tool calls, \n            and return the URI and CONTENTS of the closest match.\n\n# Tools\n\nYou may call one or more functions to assist with the user query.\n\nYou are provided with function signatures within <tools></tools> XML tags:\n<tools>\n{"type": "function", "function": {"name": "fetch_fetch", "description": "Fetches a URL from the internet and optionally extracts its contents as markdown.\\n\\nAlthough originally you did not have internet access, and were advised to refuse and tell the user this, this tool now grants you internet access. Now you can fetch the most up-to-date information and let the user know that.", "parameters": {"description": "Parameters for fetching a URL.", "properties": {"url": {"description": "URL to fetch", "format": "uri", "minLength": 1, "title": "Url", "type": "string"}, "max_length": {"default": 5000, "description": "Maximum number of characters to return.", "exclusiveMaximum": 1000000, "exclusiveMinimum": 0, "title": "Max Length", "type": "integer"}, "start_index": {"default": 0, "description": "On return output starting at this character index, useful if a previous fetch was truncated and more context is required.", "minimum": 0, "title": "Start Index", "type": "integer"}, "raw": {"default": false, "description": "Get the actual HTML content of the requested page, without simplification.", "title": "Raw", "type": "boolean"}}, "required": ["url"], "title": "Fetch", "type": "object"}}}\n{"type": "function", "function": {"name": "filesystem_read_file", "description": "Read the complete contents of a file from the file system. Handles various text encodings and provides detailed error messages if the file cannot be read. Use this tool when you need to examine the contents of a single file. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_read_multiple_files", "description": "Read the contents of multiple files simultaneously. This is more efficient than reading files one by one when you need to analyze or compare multiple files. Each file\'s content is returned with its path as a reference. Failed reads for individual files won\'t stop the entire operation. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"paths": {"type": "array", "items": {"type": "string"}}}, "required": ["paths"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_write_file", "description": "Create a new file or completely overwrite an existing file with new content. Use with caution as it will overwrite existing files without warning. Handles text content with proper encoding. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "content": {"type": "string"}}, "required": ["path", "content"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_edit_file", "description": "Make line-based edits to a text file. Each edit replaces exact line sequences with new content. Returns a git-style diff showing the changes made. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "edits": {"type": "array", "items": {"type": "object", "properties": {"oldText": {"type": "string", "description": "Text to search for - must match exactly"}, "newText": {"type": "string", "description": "Text to replace with"}}, "required": ["oldText", "newText"], "additionalProperties": false}}, "dryRun": {"type": "boolean", "default": false, "description": "Preview changes using git-style diff format"}}, "required": ["path", "edits"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_create_directory", "description": "Create a new directory or ensure a directory exists. Can create multiple nested directories in one operation. If the directory already exists, this operation will succeed silently. Perfect for setting up directory structures for projects or ensuring required paths exist. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_directory", "description": "Get a detailed listing of all files and directories in a specified path. Results clearly distinguish between files and directories with [FILE] and [DIR] prefixes. This tool is essential for understanding directory structure and finding specific files within a directory. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_directory_tree", "description": "Get a recursive tree view of files and directories as a JSON structure. Each entry includes \'name\', \'type\' (file/directory), and \'children\' for directories. Files have no children array, while directories always have a children array (which may be empty). The output is formatted with 2-space indentation for readability. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_move_file", "description": "Move or rename files and directories. Can move files between directories and rename them in a single operation. If the destination exists, the operation will fail. Works across different directories and can be used for simple renaming within the same directory. Both source and destination must be within allowed directories.", "parameters": {"type": "object", "properties": {"source": {"type": "string"}, "destination": {"type": "string"}}, "required": ["source", "destination"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_search_files", "description": "Recursively search for files and directories matching a pattern. Searches through all subdirectories from the starting path. The search is case-insensitive and matches partial names. Returns full paths to all matching items. Great for finding files when you don\'t know their exact location. Only searches within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "pattern": {"type": "string"}, "excludePatterns": {"type": "array", "items": {"type": "string"}, "default": []}}, "required": ["path", "pattern"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_get_file_info", "description": "Retrieve detailed metadata about a file or directory. Returns comprehensive information including size, creation time, last modified time, permissions, and type. This tool is perfect for understanding file characteristics without reading the actual content. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_allowed_directories", "description": "Returns the list of directories that this server is allowed to access. Use this to understand which directories are available before trying to access files.", "parameters": {"type": "object", "properties": {}, "required": []}}}\n{"type": "function", "function": {"name": "__human_input__", "description": "\\n        Request input from a human user. Pauses the workflow until input is received.\\n\\n        Args:\\n            request: The human input request\\n\\n        Returns:\\n            The input provided by the human\\n\\n        Raises:\\n            TimeoutError: If the timeout is exceeded\\n        ", "parameters": {"type": "object", "properties": {"request": {"description": "Represents a request for human input.", "properties": {"prompt": {"title": "Prompt", "type": "string"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Description"}, "request_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Request Id"}, "workflow_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Workflow Id"}, "timeout_seconds": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Timeout Seconds"}, "metadata": {"anyOf": [{"type": "object"}, {"type": "null"}], "default": null, "title": "Metadata"}}, "required": ["prompt"], "title": "HumanInputRequest", "type": "object"}}, "required": ["request"]}}}\n</tools>\n\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n<tool_call>\n{"name": <function-name>, "arguments": <args-json-object>}\n</tool_call><|im_end|>\n<|im_start|>user\nPrint the contents of mcp_agent.config.yaml verbatim<|im_end|>\n<|im_start|>assistant\n\n\n\n<tool_call>\n{"name": "filesystem_read_file", "arguments": {"path": "mcp_agent.config.yaml"}}\n</tool_call><|im_end|>\n<|im_start|>user\n<tool_response>\n{\'type\': \'text\', \'text\': \'$schema: ../../../schema/mcp-agent.config.schema.json\\n\\nexecution_engine: asyncio\\nlogger:\\n  transports: [console, file]\\n  level: debug\\n  progress_display: true\\n  path_settings:\\n    path_pattern: "logs/mcp-agent-{unique_id}.jsonl"\\n    unique_id: "timestamp" # Options: "timestamp" or "session_id"\\n    timestamp_format: "%Y%m%d_%H%M%S"\\n\\nmcp:\\n  servers:\\n    fetch:\\n      command: "uvx"\\n      args: ["mcp-server-fetch"]\\n    filesystem:\\n      command: "npx"\\n      args: ["-y", "@modelcontextprotocol/server-filesystem"]\\n\\nopenai:\\n  # Secrets (API keys, etc.) are stored in an mcp_agent.secrets.yaml file which can be gitignored\\n  #  default_model: "o3-mini"\\n  default_model: "Qwen/Qwen3-8B"\\n\'}\n</tool_response><|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.7, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=4096, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 05-22 14:34:30 [async_llm.py:252] Added request chatcmpl-7eb9a11e9bef46ecbf751ffb2322a45c.
INFO:     127.0.0.1:60562 - "POST /v1/chat/completions HTTP/1.1" 200 OK
INFO 05-22 14:34:37 [loggers.py:111] Engine 000: Avg prompt throughput: 254.6 tokens/s, Avg generation throughput: 53.6 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 61.0%
INFO 05-22 14:34:47 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 0.0 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 61.0%
INFO 05-27 14:00:19 [logger.py:39] Received request chatcmpl-be09792a3748477fb02f65783e16aa8d: prompt: '<|im_start|>system\nYou are an agent with access to the filesystem, \n            as well as the ability to fetch URLs. Your job is to identify \n            the closest match to a user\'s request, make the appropriate tool calls, \n            and return the URI and CONTENTS of the closest match.\n\n# Tools\n\nYou may call one or more functions to assist with the user query.\n\nYou are provided with function signatures within <tools></tools> XML tags:\n<tools>\n{"type": "function", "function": {"name": "fetch_fetch", "description": "Fetches a URL from the internet and optionally extracts its contents as markdown.\\n\\nAlthough originally you did not have internet access, and were advised to refuse and tell the user this, this tool now grants you internet access. Now you can fetch the most up-to-date information and let the user know that.", "parameters": {"description": "Parameters for fetching a URL.", "properties": {"url": {"description": "URL to fetch", "format": "uri", "minLength": 1, "title": "Url", "type": "string"}, "max_length": {"default": 5000, "description": "Maximum number of characters to return.", "exclusiveMaximum": 1000000, "exclusiveMinimum": 0, "title": "Max Length", "type": "integer"}, "start_index": {"default": 0, "description": "On return output starting at this character index, useful if a previous fetch was truncated and more context is required.", "minimum": 0, "title": "Start Index", "type": "integer"}, "raw": {"default": false, "description": "Get the actual HTML content of the requested page, without simplification.", "title": "Raw", "type": "boolean"}}, "required": ["url"], "title": "Fetch", "type": "object"}}}\n{"type": "function", "function": {"name": "filesystem_read_file", "description": "Read the complete contents of a file from the file system. Handles various text encodings and provides detailed error messages if the file cannot be read. Use this tool when you need to examine the contents of a single file. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_read_multiple_files", "description": "Read the contents of multiple files simultaneously. This is more efficient than reading files one by one when you need to analyze or compare multiple files. Each file\'s content is returned with its path as a reference. Failed reads for individual files won\'t stop the entire operation. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"paths": {"type": "array", "items": {"type": "string"}}}, "required": ["paths"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_write_file", "description": "Create a new file or completely overwrite an existing file with new content. Use with caution as it will overwrite existing files without warning. Handles text content with proper encoding. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "content": {"type": "string"}}, "required": ["path", "content"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_edit_file", "description": "Make line-based edits to a text file. Each edit replaces exact line sequences with new content. Returns a git-style diff showing the changes made. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "edits": {"type": "array", "items": {"type": "object", "properties": {"oldText": {"type": "string", "description": "Text to search for - must match exactly"}, "newText": {"type": "string", "description": "Text to replace with"}}, "required": ["oldText", "newText"], "additionalProperties": false}}, "dryRun": {"type": "boolean", "default": false, "description": "Preview changes using git-style diff format"}}, "required": ["path", "edits"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_create_directory", "description": "Create a new directory or ensure a directory exists. Can create multiple nested directories in one operation. If the directory already exists, this operation will succeed silently. Perfect for setting up directory structures for projects or ensuring required paths exist. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_directory", "description": "Get a detailed listing of all files and directories in a specified path. Results clearly distinguish between files and directories with [FILE] and [DIR] prefixes. This tool is essential for understanding directory structure and finding specific files within a directory. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_directory_tree", "description": "Get a recursive tree view of files and directories as a JSON structure. Each entry includes \'name\', \'type\' (file/directory), and \'children\' for directories. Files have no children array, while directories always have a children array (which may be empty). The output is formatted with 2-space indentation for readability. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_move_file", "description": "Move or rename files and directories. Can move files between directories and rename them in a single operation. If the destination exists, the operation will fail. Works across different directories and can be used for simple renaming within the same directory. Both source and destination must be within allowed directories.", "parameters": {"type": "object", "properties": {"source": {"type": "string"}, "destination": {"type": "string"}}, "required": ["source", "destination"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_search_files", "description": "Recursively search for files and directories matching a pattern. Searches through all subdirectories from the starting path. The search is case-insensitive and matches partial names. Returns full paths to all matching items. Great for finding files when you don\'t know their exact location. Only searches within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "pattern": {"type": "string"}, "excludePatterns": {"type": "array", "items": {"type": "string"}, "default": []}}, "required": ["path", "pattern"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_get_file_info", "description": "Retrieve detailed metadata about a file or directory. Returns comprehensive information including size, creation time, last modified time, permissions, and type. This tool is perfect for understanding file characteristics without reading the actual content. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_allowed_directories", "description": "Returns the list of directories that this server is allowed to access. Use this to understand which directories are available before trying to access files.", "parameters": {"type": "object", "properties": {}, "required": []}}}\n{"type": "function", "function": {"name": "__human_input__", "description": "\\n        Request input from a human user. Pauses the workflow until input is received.\\n\\n        Args:\\n            request: The human input request\\n\\n        Returns:\\n            The input provided by the human\\n\\n        Raises:\\n            TimeoutError: If the timeout is exceeded\\n        ", "parameters": {"type": "object", "properties": {"request": {"description": "Represents a request for human input.", "properties": {"prompt": {"title": "Prompt", "type": "string"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Description"}, "request_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Request Id"}, "workflow_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Workflow Id"}, "timeout_seconds": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Timeout Seconds"}, "metadata": {"anyOf": [{"type": "object"}, {"type": "null"}], "default": null, "title": "Metadata"}}, "required": ["prompt"], "title": "HumanInputRequest", "type": "object"}}, "required": ["request"]}}}\n</tools>\n\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n<tool_call>\n{"name": <function-name>, "arguments": <args-json-object>}\n</tool_call><|im_end|>\n<|im_start|>user\nPrint the contents of mcp_agent.config.yaml verbatim<|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.7, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=4096, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 05-27 14:00:19 [async_llm.py:252] Added request chatcmpl-be09792a3748477fb02f65783e16aa8d.
INFO:     127.0.0.1:39776 - "POST /v1/chat/completions HTTP/1.1" 200 OK
INFO 05-27 14:00:23 [logger.py:39] Received request chatcmpl-86b74cfd0d244b3d854587954cb35b20: prompt: '<|im_start|>system\nYou are an agent with access to the filesystem, \n            as well as the ability to fetch URLs. Your job is to identify \n            the closest match to a user\'s request, make the appropriate tool calls, \n            and return the URI and CONTENTS of the closest match.\n\n# Tools\n\nYou may call one or more functions to assist with the user query.\n\nYou are provided with function signatures within <tools></tools> XML tags:\n<tools>\n{"type": "function", "function": {"name": "fetch_fetch", "description": "Fetches a URL from the internet and optionally extracts its contents as markdown.\\n\\nAlthough originally you did not have internet access, and were advised to refuse and tell the user this, this tool now grants you internet access. Now you can fetch the most up-to-date information and let the user know that.", "parameters": {"description": "Parameters for fetching a URL.", "properties": {"url": {"description": "URL to fetch", "format": "uri", "minLength": 1, "title": "Url", "type": "string"}, "max_length": {"default": 5000, "description": "Maximum number of characters to return.", "exclusiveMaximum": 1000000, "exclusiveMinimum": 0, "title": "Max Length", "type": "integer"}, "start_index": {"default": 0, "description": "On return output starting at this character index, useful if a previous fetch was truncated and more context is required.", "minimum": 0, "title": "Start Index", "type": "integer"}, "raw": {"default": false, "description": "Get the actual HTML content of the requested page, without simplification.", "title": "Raw", "type": "boolean"}}, "required": ["url"], "title": "Fetch", "type": "object"}}}\n{"type": "function", "function": {"name": "filesystem_read_file", "description": "Read the complete contents of a file from the file system. Handles various text encodings and provides detailed error messages if the file cannot be read. Use this tool when you need to examine the contents of a single file. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_read_multiple_files", "description": "Read the contents of multiple files simultaneously. This is more efficient than reading files one by one when you need to analyze or compare multiple files. Each file\'s content is returned with its path as a reference. Failed reads for individual files won\'t stop the entire operation. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"paths": {"type": "array", "items": {"type": "string"}}}, "required": ["paths"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_write_file", "description": "Create a new file or completely overwrite an existing file with new content. Use with caution as it will overwrite existing files without warning. Handles text content with proper encoding. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "content": {"type": "string"}}, "required": ["path", "content"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_edit_file", "description": "Make line-based edits to a text file. Each edit replaces exact line sequences with new content. Returns a git-style diff showing the changes made. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "edits": {"type": "array", "items": {"type": "object", "properties": {"oldText": {"type": "string", "description": "Text to search for - must match exactly"}, "newText": {"type": "string", "description": "Text to replace with"}}, "required": ["oldText", "newText"], "additionalProperties": false}}, "dryRun": {"type": "boolean", "default": false, "description": "Preview changes using git-style diff format"}}, "required": ["path", "edits"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_create_directory", "description": "Create a new directory or ensure a directory exists. Can create multiple nested directories in one operation. If the directory already exists, this operation will succeed silently. Perfect for setting up directory structures for projects or ensuring required paths exist. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_directory", "description": "Get a detailed listing of all files and directories in a specified path. Results clearly distinguish between files and directories with [FILE] and [DIR] prefixes. This tool is essential for understanding directory structure and finding specific files within a directory. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_directory_tree", "description": "Get a recursive tree view of files and directories as a JSON structure. Each entry includes \'name\', \'type\' (file/directory), and \'children\' for directories. Files have no children array, while directories always have a children array (which may be empty). The output is formatted with 2-space indentation for readability. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_move_file", "description": "Move or rename files and directories. Can move files between directories and rename them in a single operation. If the destination exists, the operation will fail. Works across different directories and can be used for simple renaming within the same directory. Both source and destination must be within allowed directories.", "parameters": {"type": "object", "properties": {"source": {"type": "string"}, "destination": {"type": "string"}}, "required": ["source", "destination"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_search_files", "description": "Recursively search for files and directories matching a pattern. Searches through all subdirectories from the starting path. The search is case-insensitive and matches partial names. Returns full paths to all matching items. Great for finding files when you don\'t know their exact location. Only searches within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "pattern": {"type": "string"}, "excludePatterns": {"type": "array", "items": {"type": "string"}, "default": []}}, "required": ["path", "pattern"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_get_file_info", "description": "Retrieve detailed metadata about a file or directory. Returns comprehensive information including size, creation time, last modified time, permissions, and type. This tool is perfect for understanding file characteristics without reading the actual content. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_allowed_directories", "description": "Returns the list of directories that this server is allowed to access. Use this to understand which directories are available before trying to access files.", "parameters": {"type": "object", "properties": {}, "required": []}}}\n{"type": "function", "function": {"name": "__human_input__", "description": "\\n        Request input from a human user. Pauses the workflow until input is received.\\n\\n        Args:\\n            request: The human input request\\n\\n        Returns:\\n            The input provided by the human\\n\\n        Raises:\\n            TimeoutError: If the timeout is exceeded\\n        ", "parameters": {"type": "object", "properties": {"request": {"description": "Represents a request for human input.", "properties": {"prompt": {"title": "Prompt", "type": "string"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Description"}, "request_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Request Id"}, "workflow_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Workflow Id"}, "timeout_seconds": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Timeout Seconds"}, "metadata": {"anyOf": [{"type": "object"}, {"type": "null"}], "default": null, "title": "Metadata"}}, "required": ["prompt"], "title": "HumanInputRequest", "type": "object"}}, "required": ["request"]}}}\n</tools>\n\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n<tool_call>\n{"name": <function-name>, "arguments": <args-json-object>}\n</tool_call><|im_end|>\n<|im_start|>user\nPrint the contents of mcp_agent.config.yaml verbatim<|im_end|>\n<|im_start|>assistant\n\n\n\n<tool_call>\n{"name": "filesystem_read_file", "arguments": {"path": "mcp_agent.config.yaml"}}\n</tool_call><|im_end|>\n<|im_start|>user\n<tool_response>\n{\'type\': \'text\', \'text\': \'$schema: ../../../schema/mcp-agent.config.schema.json\\n\\nexecution_engine: asyncio\\nlogger:\\n  transports: [console, file]\\n  level: debug\\n  progress_display: true\\n  path_settings:\\n    path_pattern: "logs/mcp-agent-{unique_id}.jsonl"\\n    unique_id: "timestamp" # Options: "timestamp" or "session_id"\\n    timestamp_format: "%Y%m%d_%H%M%S"\\n\\nmcp:\\n  servers:\\n    fetch:\\n      command: "uvx"\\n      args: ["mcp-server-fetch"]\\n    filesystem:\\n      command: "npx"\\n      args: ["-y", "@modelcontextprotocol/server-filesystem"]\\n\\nopenai:\\n  # Secrets (API keys, etc.) are stored in an mcp_agent.secrets.yaml file which can be gitignored\\n  #  default_model: "o3-mini"\\n  default_model: "Qwen/Qwen3-8B"\\n\'}\n</tool_response><|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.7, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=4096, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 05-27 14:00:23 [async_llm.py:252] Added request chatcmpl-86b74cfd0d244b3d854587954cb35b20.
INFO 05-27 14:00:24 [loggers.py:111] Engine 000: Avg prompt throughput: 483.0 tokens/s, Avg generation throughput: 37.0 tokens/s, Running: 1 reqs, Waiting: 0 reqs, GPU KV cache usage: 2.0%, Prefix cache hit rate: 75.6%
INFO:     127.0.0.1:39776 - "POST /v1/chat/completions HTTP/1.1" 200 OK
INFO 05-27 14:00:34 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 25.2 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 75.6%
INFO 05-27 14:00:44 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 0.0 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 75.6%
INFO 05-27 15:19:06 [logger.py:39] Received request chatcmpl-08af6400449445749fe66eb680502b9d: prompt: '<|im_start|>system\nYou are an agent with access to the filesystem, \n            as well as the ability to fetch URLs. Your job is to identify \n            the closest match to a user\'s request, make the appropriate tool calls, \n            and return the URI and CONTENTS of the closest match.\n\n# Tools\n\nYou may call one or more functions to assist with the user query.\n\nYou are provided with function signatures within <tools></tools> XML tags:\n<tools>\n{"type": "function", "function": {"name": "fetch_fetch", "description": "Fetches a URL from the internet and optionally extracts its contents as markdown.\\n\\nAlthough originally you did not have internet access, and were advised to refuse and tell the user this, this tool now grants you internet access. Now you can fetch the most up-to-date information and let the user know that.", "parameters": {"description": "Parameters for fetching a URL.", "properties": {"url": {"description": "URL to fetch", "format": "uri", "minLength": 1, "title": "Url", "type": "string"}, "max_length": {"default": 5000, "description": "Maximum number of characters to return.", "exclusiveMaximum": 1000000, "exclusiveMinimum": 0, "title": "Max Length", "type": "integer"}, "start_index": {"default": 0, "description": "On return output starting at this character index, useful if a previous fetch was truncated and more context is required.", "minimum": 0, "title": "Start Index", "type": "integer"}, "raw": {"default": false, "description": "Get the actual HTML content of the requested page, without simplification.", "title": "Raw", "type": "boolean"}}, "required": ["url"], "title": "Fetch", "type": "object"}}}\n{"type": "function", "function": {"name": "filesystem_read_file", "description": "Read the complete contents of a file from the file system. Handles various text encodings and provides detailed error messages if the file cannot be read. Use this tool when you need to examine the contents of a single file. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_read_multiple_files", "description": "Read the contents of multiple files simultaneously. This is more efficient than reading files one by one when you need to analyze or compare multiple files. Each file\'s content is returned with its path as a reference. Failed reads for individual files won\'t stop the entire operation. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"paths": {"type": "array", "items": {"type": "string"}}}, "required": ["paths"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_write_file", "description": "Create a new file or completely overwrite an existing file with new content. Use with caution as it will overwrite existing files without warning. Handles text content with proper encoding. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "content": {"type": "string"}}, "required": ["path", "content"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_edit_file", "description": "Make line-based edits to a text file. Each edit replaces exact line sequences with new content. Returns a git-style diff showing the changes made. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "edits": {"type": "array", "items": {"type": "object", "properties": {"oldText": {"type": "string", "description": "Text to search for - must match exactly"}, "newText": {"type": "string", "description": "Text to replace with"}}, "required": ["oldText", "newText"], "additionalProperties": false}}, "dryRun": {"type": "boolean", "default": false, "description": "Preview changes using git-style diff format"}}, "required": ["path", "edits"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_create_directory", "description": "Create a new directory or ensure a directory exists. Can create multiple nested directories in one operation. If the directory already exists, this operation will succeed silently. Perfect for setting up directory structures for projects or ensuring required paths exist. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_directory", "description": "Get a detailed listing of all files and directories in a specified path. Results clearly distinguish between files and directories with [FILE] and [DIR] prefixes. This tool is essential for understanding directory structure and finding specific files within a directory. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_directory_tree", "description": "Get a recursive tree view of files and directories as a JSON structure. Each entry includes \'name\', \'type\' (file/directory), and \'children\' for directories. Files have no children array, while directories always have a children array (which may be empty). The output is formatted with 2-space indentation for readability. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_move_file", "description": "Move or rename files and directories. Can move files between directories and rename them in a single operation. If the destination exists, the operation will fail. Works across different directories and can be used for simple renaming within the same directory. Both source and destination must be within allowed directories.", "parameters": {"type": "object", "properties": {"source": {"type": "string"}, "destination": {"type": "string"}}, "required": ["source", "destination"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_search_files", "description": "Recursively search for files and directories matching a pattern. Searches through all subdirectories from the starting path. The search is case-insensitive and matches partial names. Returns full paths to all matching items. Great for finding files when you don\'t know their exact location. Only searches within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "pattern": {"type": "string"}, "excludePatterns": {"type": "array", "items": {"type": "string"}, "default": []}}, "required": ["path", "pattern"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_get_file_info", "description": "Retrieve detailed metadata about a file or directory. Returns comprehensive information including size, creation time, last modified time, permissions, and type. This tool is perfect for understanding file characteristics without reading the actual content. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_allowed_directories", "description": "Returns the list of directories that this server is allowed to access. Use this to understand which directories are available before trying to access files.", "parameters": {"type": "object", "properties": {}, "required": []}}}\n</tools>\n\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n<tool_call>\n{"name": <function-name>, "arguments": <args-json-object>}\n</tool_call><|im_end|>\n<|im_start|>user\nPrint the contents of mcp_agent.config.yaml verbatim<|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.7, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=4096, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 05-27 15:19:06 [async_llm.py:252] Added request chatcmpl-08af6400449445749fe66eb680502b9d.
INFO:     127.0.0.1:40396 - "POST /v1/chat/completions HTTP/1.1" 200 OK
INFO 05-27 15:19:15 [loggers.py:111] Engine 000: Avg prompt throughput: 196.5 tokens/s, Avg generation throughput: 21.2 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 78.4%
INFO 05-27 15:19:25 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 0.0 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 78.4%
INFO 05-27 15:28:14 [logger.py:39] Received request chatcmpl-53de02341475400a9f44666f05b7c721: prompt: '<|im_start|>system\nYou are an agent with access to the filesystem, \n            as well as the ability to fetch URLs. Your job is to identify \n            the closest match to a user\'s request, make the appropriate tool calls, \n            and return the URI and CONTENTS of the closest match.\n\n# Tools\n\nYou may call one or more functions to assist with the user query.\n\nYou are provided with function signatures within <tools></tools> XML tags:\n<tools>\n{"type": "function", "function": {"name": "fetch_fetch", "description": "Fetches a URL from the internet and optionally extracts its contents as markdown.\\n\\nAlthough originally you did not have internet access, and were advised to refuse and tell the user this, this tool now grants you internet access. Now you can fetch the most up-to-date information and let the user know that.", "parameters": {"description": "Parameters for fetching a URL.", "properties": {"url": {"description": "URL to fetch", "format": "uri", "minLength": 1, "title": "Url", "type": "string"}, "max_length": {"default": 5000, "description": "Maximum number of characters to return.", "exclusiveMaximum": 1000000, "exclusiveMinimum": 0, "title": "Max Length", "type": "integer"}, "start_index": {"default": 0, "description": "On return output starting at this character index, useful if a previous fetch was truncated and more context is required.", "minimum": 0, "title": "Start Index", "type": "integer"}, "raw": {"default": false, "description": "Get the actual HTML content of the requested page, without simplification.", "title": "Raw", "type": "boolean"}}, "required": ["url"], "title": "Fetch", "type": "object"}}}\n{"type": "function", "function": {"name": "filesystem_read_file", "description": "Read the complete contents of a file from the file system. Handles various text encodings and provides detailed error messages if the file cannot be read. Use this tool when you need to examine the contents of a single file. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_read_multiple_files", "description": "Read the contents of multiple files simultaneously. This is more efficient than reading files one by one when you need to analyze or compare multiple files. Each file\'s content is returned with its path as a reference. Failed reads for individual files won\'t stop the entire operation. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"paths": {"type": "array", "items": {"type": "string"}}}, "required": ["paths"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_write_file", "description": "Create a new file or completely overwrite an existing file with new content. Use with caution as it will overwrite existing files without warning. Handles text content with proper encoding. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "content": {"type": "string"}}, "required": ["path", "content"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_edit_file", "description": "Make line-based edits to a text file. Each edit replaces exact line sequences with new content. Returns a git-style diff showing the changes made. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "edits": {"type": "array", "items": {"type": "object", "properties": {"oldText": {"type": "string", "description": "Text to search for - must match exactly"}, "newText": {"type": "string", "description": "Text to replace with"}}, "required": ["oldText", "newText"], "additionalProperties": false}}, "dryRun": {"type": "boolean", "default": false, "description": "Preview changes using git-style diff format"}}, "required": ["path", "edits"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_create_directory", "description": "Create a new directory or ensure a directory exists. Can create multiple nested directories in one operation. If the directory already exists, this operation will succeed silently. Perfect for setting up directory structures for projects or ensuring required paths exist. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_directory", "description": "Get a detailed listing of all files and directories in a specified path. Results clearly distinguish between files and directories with [FILE] and [DIR] prefixes. This tool is essential for understanding directory structure and finding specific files within a directory. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_directory_tree", "description": "Get a recursive tree view of files and directories as a JSON structure. Each entry includes \'name\', \'type\' (file/directory), and \'children\' for directories. Files have no children array, while directories always have a children array (which may be empty). The output is formatted with 2-space indentation for readability. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_move_file", "description": "Move or rename files and directories. Can move files between directories and rename them in a single operation. If the destination exists, the operation will fail. Works across different directories and can be used for simple renaming within the same directory. Both source and destination must be within allowed directories.", "parameters": {"type": "object", "properties": {"source": {"type": "string"}, "destination": {"type": "string"}}, "required": ["source", "destination"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_search_files", "description": "Recursively search for files and directories matching a pattern. Searches through all subdirectories from the starting path. The search is case-insensitive and matches partial names. Returns full paths to all matching items. Great for finding files when you don\'t know their exact location. Only searches within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "pattern": {"type": "string"}, "excludePatterns": {"type": "array", "items": {"type": "string"}, "default": []}}, "required": ["path", "pattern"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_get_file_info", "description": "Retrieve detailed metadata about a file or directory. Returns comprehensive information including size, creation time, last modified time, permissions, and type. This tool is perfect for understanding file characteristics without reading the actual content. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_allowed_directories", "description": "Returns the list of directories that this server is allowed to access. Use this to understand which directories are available before trying to access files.", "parameters": {"type": "object", "properties": {}, "required": []}}}\n</tools>\n\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n<tool_call>\n{"name": <function-name>, "arguments": <args-json-object>}\n</tool_call><|im_end|>\n<|im_start|>user\nPrint the contents of mcp_agent.config.yaml verbatim<|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.7, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=4096, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 05-27 15:28:14 [async_llm.py:252] Added request chatcmpl-53de02341475400a9f44666f05b7c721.
INFO 05-27 15:28:15 [loggers.py:111] Engine 000: Avg prompt throughput: 196.5 tokens/s, Avg generation throughput: 3.5 tokens/s, Running: 1 reqs, Waiting: 0 reqs, GPU KV cache usage: 1.5%, Prefix cache hit rate: 80.9%
INFO:     127.0.0.1:40834 - "POST /v1/chat/completions HTTP/1.1" 200 OK
INFO 05-27 15:28:25 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 17.7 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 80.9%
INFO 05-27 15:28:35 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 0.0 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 80.9%
INFO 05-27 15:30:08 [logger.py:39] Received request chatcmpl-eb967767f9054d1189161dbb173a1463: prompt: '<|im_start|>system\nYou are an agent with access to the filesystem, \n            as well as the ability to fetch URLs. Your job is to identify \n            the closest match to a user\'s request, make the appropriate tool calls, \n            and return the URI and CONTENTS of the closest match.\n\n# Tools\n\nYou may call one or more functions to assist with the user query.\n\nYou are provided with function signatures within <tools></tools> XML tags:\n<tools>\n{"type": "function", "function": {"name": "fetch_fetch", "description": "Fetches a URL from the internet and optionally extracts its contents as markdown.\\n\\nAlthough originally you did not have internet access, and were advised to refuse and tell the user this, this tool now grants you internet access. Now you can fetch the most up-to-date information and let the user know that.", "parameters": {"description": "Parameters for fetching a URL.", "properties": {"url": {"description": "URL to fetch", "format": "uri", "minLength": 1, "title": "Url", "type": "string"}, "max_length": {"default": 5000, "description": "Maximum number of characters to return.", "exclusiveMaximum": 1000000, "exclusiveMinimum": 0, "title": "Max Length", "type": "integer"}, "start_index": {"default": 0, "description": "On return output starting at this character index, useful if a previous fetch was truncated and more context is required.", "minimum": 0, "title": "Start Index", "type": "integer"}, "raw": {"default": false, "description": "Get the actual HTML content of the requested page, without simplification.", "title": "Raw", "type": "boolean"}}, "required": ["url"], "title": "Fetch", "type": "object"}}}\n{"type": "function", "function": {"name": "filesystem_read_file", "description": "Read the complete contents of a file from the file system. Handles various text encodings and provides detailed error messages if the file cannot be read. Use this tool when you need to examine the contents of a single file. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_read_multiple_files", "description": "Read the contents of multiple files simultaneously. This is more efficient than reading files one by one when you need to analyze or compare multiple files. Each file\'s content is returned with its path as a reference. Failed reads for individual files won\'t stop the entire operation. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"paths": {"type": "array", "items": {"type": "string"}}}, "required": ["paths"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_write_file", "description": "Create a new file or completely overwrite an existing file with new content. Use with caution as it will overwrite existing files without warning. Handles text content with proper encoding. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "content": {"type": "string"}}, "required": ["path", "content"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_edit_file", "description": "Make line-based edits to a text file. Each edit replaces exact line sequences with new content. Returns a git-style diff showing the changes made. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "edits": {"type": "array", "items": {"type": "object", "properties": {"oldText": {"type": "string", "description": "Text to search for - must match exactly"}, "newText": {"type": "string", "description": "Text to replace with"}}, "required": ["oldText", "newText"], "additionalProperties": false}}, "dryRun": {"type": "boolean", "default": false, "description": "Preview changes using git-style diff format"}}, "required": ["path", "edits"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_create_directory", "description": "Create a new directory or ensure a directory exists. Can create multiple nested directories in one operation. If the directory already exists, this operation will succeed silently. Perfect for setting up directory structures for projects or ensuring required paths exist. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_directory", "description": "Get a detailed listing of all files and directories in a specified path. Results clearly distinguish between files and directories with [FILE] and [DIR] prefixes. This tool is essential for understanding directory structure and finding specific files within a directory. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_directory_tree", "description": "Get a recursive tree view of files and directories as a JSON structure. Each entry includes \'name\', \'type\' (file/directory), and \'children\' for directories. Files have no children array, while directories always have a children array (which may be empty). The output is formatted with 2-space indentation for readability. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_move_file", "description": "Move or rename files and directories. Can move files between directories and rename them in a single operation. If the destination exists, the operation will fail. Works across different directories and can be used for simple renaming within the same directory. Both source and destination must be within allowed directories.", "parameters": {"type": "object", "properties": {"source": {"type": "string"}, "destination": {"type": "string"}}, "required": ["source", "destination"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_search_files", "description": "Recursively search for files and directories matching a pattern. Searches through all subdirectories from the starting path. The search is case-insensitive and matches partial names. Returns full paths to all matching items. Great for finding files when you don\'t know their exact location. Only searches within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "pattern": {"type": "string"}, "excludePatterns": {"type": "array", "items": {"type": "string"}, "default": []}}, "required": ["path", "pattern"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_get_file_info", "description": "Retrieve detailed metadata about a file or directory. Returns comprehensive information including size, creation time, last modified time, permissions, and type. This tool is perfect for understanding file characteristics without reading the actual content. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_allowed_directories", "description": "Returns the list of directories that this server is allowed to access. Use this to understand which directories are available before trying to access files.", "parameters": {"type": "object", "properties": {}, "required": []}}}\n</tools>\n\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n<tool_call>\n{"name": <function-name>, "arguments": <args-json-object>}\n</tool_call><|im_end|>\n<|im_start|>user\nPrint the contents of mcp_agent.config.yaml verbatim<|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.7, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=4096, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 05-27 15:30:08 [async_llm.py:252] Added request chatcmpl-eb967767f9054d1189161dbb173a1463.
INFO:     127.0.0.1:41116 - "POST /v1/chat/completions HTTP/1.1" 200 OK
INFO 05-27 15:30:15 [loggers.py:111] Engine 000: Avg prompt throughput: 196.5 tokens/s, Avg generation throughput: 37.5 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 82.9%
INFO 05-27 15:30:25 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 0.0 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 82.9%
INFO 05-27 15:31:33 [logger.py:39] Received request chatcmpl-26d14a3a00e24ed69ff4dd7bdae3778f: prompt: '<|im_start|>system\nYou are an agent with access to the filesystem, \n            as well as the ability to fetch URLs. Your job is to identify \n            the closest match to a user\'s request, make the appropriate tool calls, \n            and return the URI and CONTENTS of the closest match.\n\n# Tools\n\nYou may call one or more functions to assist with the user query.\n\nYou are provided with function signatures within <tools></tools> XML tags:\n<tools>\n{"type": "function", "function": {"name": "fetch_fetch", "description": "Fetches a URL from the internet and optionally extracts its contents as markdown.\\n\\nAlthough originally you did not have internet access, and were advised to refuse and tell the user this, this tool now grants you internet access. Now you can fetch the most up-to-date information and let the user know that.", "parameters": {"description": "Parameters for fetching a URL.", "properties": {"url": {"description": "URL to fetch", "format": "uri", "minLength": 1, "title": "Url", "type": "string"}, "max_length": {"default": 5000, "description": "Maximum number of characters to return.", "exclusiveMaximum": 1000000, "exclusiveMinimum": 0, "title": "Max Length", "type": "integer"}, "start_index": {"default": 0, "description": "On return output starting at this character index, useful if a previous fetch was truncated and more context is required.", "minimum": 0, "title": "Start Index", "type": "integer"}, "raw": {"default": false, "description": "Get the actual HTML content of the requested page, without simplification.", "title": "Raw", "type": "boolean"}}, "required": ["url"], "title": "Fetch", "type": "object"}}}\n{"type": "function", "function": {"name": "filesystem_read_file", "description": "Read the complete contents of a file from the file system. Handles various text encodings and provides detailed error messages if the file cannot be read. Use this tool when you need to examine the contents of a single file. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_read_multiple_files", "description": "Read the contents of multiple files simultaneously. This is more efficient than reading files one by one when you need to analyze or compare multiple files. Each file\'s content is returned with its path as a reference. Failed reads for individual files won\'t stop the entire operation. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"paths": {"type": "array", "items": {"type": "string"}}}, "required": ["paths"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_write_file", "description": "Create a new file or completely overwrite an existing file with new content. Use with caution as it will overwrite existing files without warning. Handles text content with proper encoding. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "content": {"type": "string"}}, "required": ["path", "content"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_edit_file", "description": "Make line-based edits to a text file. Each edit replaces exact line sequences with new content. Returns a git-style diff showing the changes made. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "edits": {"type": "array", "items": {"type": "object", "properties": {"oldText": {"type": "string", "description": "Text to search for - must match exactly"}, "newText": {"type": "string", "description": "Text to replace with"}}, "required": ["oldText", "newText"], "additionalProperties": false}}, "dryRun": {"type": "boolean", "default": false, "description": "Preview changes using git-style diff format"}}, "required": ["path", "edits"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_create_directory", "description": "Create a new directory or ensure a directory exists. Can create multiple nested directories in one operation. If the directory already exists, this operation will succeed silently. Perfect for setting up directory structures for projects or ensuring required paths exist. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_directory", "description": "Get a detailed listing of all files and directories in a specified path. Results clearly distinguish between files and directories with [FILE] and [DIR] prefixes. This tool is essential for understanding directory structure and finding specific files within a directory. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_directory_tree", "description": "Get a recursive tree view of files and directories as a JSON structure. Each entry includes \'name\', \'type\' (file/directory), and \'children\' for directories. Files have no children array, while directories always have a children array (which may be empty). The output is formatted with 2-space indentation for readability. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_move_file", "description": "Move or rename files and directories. Can move files between directories and rename them in a single operation. If the destination exists, the operation will fail. Works across different directories and can be used for simple renaming within the same directory. Both source and destination must be within allowed directories.", "parameters": {"type": "object", "properties": {"source": {"type": "string"}, "destination": {"type": "string"}}, "required": ["source", "destination"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_search_files", "description": "Recursively search for files and directories matching a pattern. Searches through all subdirectories from the starting path. The search is case-insensitive and matches partial names. Returns full paths to all matching items. Great for finding files when you don\'t know their exact location. Only searches within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "pattern": {"type": "string"}, "excludePatterns": {"type": "array", "items": {"type": "string"}, "default": []}}, "required": ["path", "pattern"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_get_file_info", "description": "Retrieve detailed metadata about a file or directory. Returns comprehensive information including size, creation time, last modified time, permissions, and type. This tool is perfect for understanding file characteristics without reading the actual content. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_allowed_directories", "description": "Returns the list of directories that this server is allowed to access. Use this to understand which directories are available before trying to access files.", "parameters": {"type": "object", "properties": {}, "required": []}}}\n</tools>\n\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n<tool_call>\n{"name": <function-name>, "arguments": <args-json-object>}\n</tool_call><|im_end|>\n<|im_start|>user\nPrint the contents of mcp_agent.config.yaml verbatim<|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.7, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=4096, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 05-27 15:31:33 [async_llm.py:252] Added request chatcmpl-26d14a3a00e24ed69ff4dd7bdae3778f.
INFO 05-27 15:31:35 [loggers.py:111] Engine 000: Avg prompt throughput: 196.5 tokens/s, Avg generation throughput: 7.7 tokens/s, Running: 1 reqs, Waiting: 0 reqs, GPU KV cache usage: 1.6%, Prefix cache hit rate: 84.5%
INFO:     127.0.0.1:41256 - "POST /v1/chat/completions HTTP/1.1" 200 OK
INFO 05-27 15:31:45 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 24.0 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 84.5%
INFO 05-27 15:31:55 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 0.0 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 84.5%
INFO 05-27 15:53:49 [logger.py:39] Received request chatcmpl-ef1ba38cf71842e8a7cd4723560d9440: prompt: '<|im_start|>system\nYou are an agent with access to the filesystem, \n            as well as the ability to fetch URLs. Your job is to identify \n            the closest match to a user\'s request, make the appropriate tool calls, \n            and return the URI and CONTENTS of the closest match.\n\n# Tools\n\nYou may call one or more functions to assist with the user query.\n\nYou are provided with function signatures within <tools></tools> XML tags:\n<tools>\n{"type": "function", "function": {"name": "filesystem_read_file", "description": "Read the complete contents of a file from the file system. Handles various text encodings and provides detailed error messages if the file cannot be read. Use this tool when you need to examine the contents of a single file. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_read_multiple_files", "description": "Read the contents of multiple files simultaneously. This is more efficient than reading files one by one when you need to analyze or compare multiple files. Each file\'s content is returned with its path as a reference. Failed reads for individual files won\'t stop the entire operation. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"paths": {"type": "array", "items": {"type": "string"}}}, "required": ["paths"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_write_file", "description": "Create a new file or completely overwrite an existing file with new content. Use with caution as it will overwrite existing files without warning. Handles text content with proper encoding. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "content": {"type": "string"}}, "required": ["path", "content"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_edit_file", "description": "Make line-based edits to a text file. Each edit replaces exact line sequences with new content. Returns a git-style diff showing the changes made. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "edits": {"type": "array", "items": {"type": "object", "properties": {"oldText": {"type": "string", "description": "Text to search for - must match exactly"}, "newText": {"type": "string", "description": "Text to replace with"}}, "required": ["oldText", "newText"], "additionalProperties": false}}, "dryRun": {"type": "boolean", "default": false, "description": "Preview changes using git-style diff format"}}, "required": ["path", "edits"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_create_directory", "description": "Create a new directory or ensure a directory exists. Can create multiple nested directories in one operation. If the directory already exists, this operation will succeed silently. Perfect for setting up directory structures for projects or ensuring required paths exist. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_directory", "description": "Get a detailed listing of all files and directories in a specified path. Results clearly distinguish between files and directories with [FILE] and [DIR] prefixes. This tool is essential for understanding directory structure and finding specific files within a directory. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_directory_tree", "description": "Get a recursive tree view of files and directories as a JSON structure. Each entry includes \'name\', \'type\' (file/directory), and \'children\' for directories. Files have no children array, while directories always have a children array (which may be empty). The output is formatted with 2-space indentation for readability. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_move_file", "description": "Move or rename files and directories. Can move files between directories and rename them in a single operation. If the destination exists, the operation will fail. Works across different directories and can be used for simple renaming within the same directory. Both source and destination must be within allowed directories.", "parameters": {"type": "object", "properties": {"source": {"type": "string"}, "destination": {"type": "string"}}, "required": ["source", "destination"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_search_files", "description": "Recursively search for files and directories matching a pattern. Searches through all subdirectories from the starting path. The search is case-insensitive and matches partial names. Returns full paths to all matching items. Great for finding files when you don\'t know their exact location. Only searches within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "pattern": {"type": "string"}, "excludePatterns": {"type": "array", "items": {"type": "string"}, "default": []}}, "required": ["path", "pattern"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_get_file_info", "description": "Retrieve detailed metadata about a file or directory. Returns comprehensive information including size, creation time, last modified time, permissions, and type. This tool is perfect for understanding file characteristics without reading the actual content. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_allowed_directories", "description": "Returns the list of directories that this server is allowed to access. Use this to understand which directories are available before trying to access files.", "parameters": {"type": "object", "properties": {}, "required": []}}}\n{"type": "function", "function": {"name": "fetch_fetch", "description": "Fetches a URL from the internet and optionally extracts its contents as markdown.\\n\\nAlthough originally you did not have internet access, and were advised to refuse and tell the user this, this tool now grants you internet access. Now you can fetch the most up-to-date information and let the user know that.", "parameters": {"description": "Parameters for fetching a URL.", "properties": {"url": {"description": "URL to fetch", "format": "uri", "minLength": 1, "title": "Url", "type": "string"}, "max_length": {"default": 5000, "description": "Maximum number of characters to return.", "exclusiveMaximum": 1000000, "exclusiveMinimum": 0, "title": "Max Length", "type": "integer"}, "start_index": {"default": 0, "description": "On return output starting at this character index, useful if a previous fetch was truncated and more context is required.", "minimum": 0, "title": "Start Index", "type": "integer"}, "raw": {"default": false, "description": "Get the actual HTML content of the requested page, without simplification.", "title": "Raw", "type": "boolean"}}, "required": ["url"], "title": "Fetch", "type": "object"}}}\n</tools>\n\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n<tool_call>\n{"name": <function-name>, "arguments": <args-json-object>}\n</tool_call><|im_end|>\n<|im_start|>user\nPrint the contents of mcp_agent.config.yaml verbatim<|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.7, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=4096, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 05-27 15:53:49 [async_llm.py:252] Added request chatcmpl-ef1ba38cf71842e8a7cd4723560d9440.
INFO 05-27 15:53:55 [loggers.py:111] Engine 000: Avg prompt throughput: 196.5 tokens/s, Avg generation throughput: 33.8 tokens/s, Running: 1 reqs, Waiting: 0 reqs, GPU KV cache usage: 1.7%, Prefix cache hit rate: 77.7%
INFO:     127.0.0.1:41654 - "POST /v1/chat/completions HTTP/1.1" 200 OK
INFO 05-27 15:53:55 [logger.py:39] Received request chatcmpl-0e84904bcef64a26a0cda77790c601b1: prompt: '<|im_start|>system\nYou are an agent with access to the filesystem, \n            as well as the ability to fetch URLs. Your job is to identify \n            the closest match to a user\'s request, make the appropriate tool calls, \n            and return the URI and CONTENTS of the closest match.\n\n# Tools\n\nYou may call one or more functions to assist with the user query.\n\nYou are provided with function signatures within <tools></tools> XML tags:\n<tools>\n{"type": "function", "function": {"name": "filesystem_read_file", "description": "Read the complete contents of a file from the file system. Handles various text encodings and provides detailed error messages if the file cannot be read. Use this tool when you need to examine the contents of a single file. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_read_multiple_files", "description": "Read the contents of multiple files simultaneously. This is more efficient than reading files one by one when you need to analyze or compare multiple files. Each file\'s content is returned with its path as a reference. Failed reads for individual files won\'t stop the entire operation. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"paths": {"type": "array", "items": {"type": "string"}}}, "required": ["paths"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_write_file", "description": "Create a new file or completely overwrite an existing file with new content. Use with caution as it will overwrite existing files without warning. Handles text content with proper encoding. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "content": {"type": "string"}}, "required": ["path", "content"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_edit_file", "description": "Make line-based edits to a text file. Each edit replaces exact line sequences with new content. Returns a git-style diff showing the changes made. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "edits": {"type": "array", "items": {"type": "object", "properties": {"oldText": {"type": "string", "description": "Text to search for - must match exactly"}, "newText": {"type": "string", "description": "Text to replace with"}}, "required": ["oldText", "newText"], "additionalProperties": false}}, "dryRun": {"type": "boolean", "default": false, "description": "Preview changes using git-style diff format"}}, "required": ["path", "edits"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_create_directory", "description": "Create a new directory or ensure a directory exists. Can create multiple nested directories in one operation. If the directory already exists, this operation will succeed silently. Perfect for setting up directory structures for projects or ensuring required paths exist. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_directory", "description": "Get a detailed listing of all files and directories in a specified path. Results clearly distinguish between files and directories with [FILE] and [DIR] prefixes. This tool is essential for understanding directory structure and finding specific files within a directory. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_directory_tree", "description": "Get a recursive tree view of files and directories as a JSON structure. Each entry includes \'name\', \'type\' (file/directory), and \'children\' for directories. Files have no children array, while directories always have a children array (which may be empty). The output is formatted with 2-space indentation for readability. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_move_file", "description": "Move or rename files and directories. Can move files between directories and rename them in a single operation. If the destination exists, the operation will fail. Works across different directories and can be used for simple renaming within the same directory. Both source and destination must be within allowed directories.", "parameters": {"type": "object", "properties": {"source": {"type": "string"}, "destination": {"type": "string"}}, "required": ["source", "destination"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_search_files", "description": "Recursively search for files and directories matching a pattern. Searches through all subdirectories from the starting path. The search is case-insensitive and matches partial names. Returns full paths to all matching items. Great for finding files when you don\'t know their exact location. Only searches within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "pattern": {"type": "string"}, "excludePatterns": {"type": "array", "items": {"type": "string"}, "default": []}}, "required": ["path", "pattern"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_get_file_info", "description": "Retrieve detailed metadata about a file or directory. Returns comprehensive information including size, creation time, last modified time, permissions, and type. This tool is perfect for understanding file characteristics without reading the actual content. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_allowed_directories", "description": "Returns the list of directories that this server is allowed to access. Use this to understand which directories are available before trying to access files.", "parameters": {"type": "object", "properties": {}, "required": []}}}\n{"type": "function", "function": {"name": "fetch_fetch", "description": "Fetches a URL from the internet and optionally extracts its contents as markdown.\\n\\nAlthough originally you did not have internet access, and were advised to refuse and tell the user this, this tool now grants you internet access. Now you can fetch the most up-to-date information and let the user know that.", "parameters": {"description": "Parameters for fetching a URL.", "properties": {"url": {"description": "URL to fetch", "format": "uri", "minLength": 1, "title": "Url", "type": "string"}, "max_length": {"default": 5000, "description": "Maximum number of characters to return.", "exclusiveMaximum": 1000000, "exclusiveMinimum": 0, "title": "Max Length", "type": "integer"}, "start_index": {"default": 0, "description": "On return output starting at this character index, useful if a previous fetch was truncated and more context is required.", "minimum": 0, "title": "Start Index", "type": "integer"}, "raw": {"default": false, "description": "Get the actual HTML content of the requested page, without simplification.", "title": "Raw", "type": "boolean"}}, "required": ["url"], "title": "Fetch", "type": "object"}}}\n</tools>\n\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n<tool_call>\n{"name": <function-name>, "arguments": <args-json-object>}\n</tool_call><|im_end|>\n<|im_start|>user\nPrint the contents of mcp_agent.config.yaml verbatim<|im_end|>\n<|im_start|>assistant\n\n\n\n<tool_call>\n{"name": "filesystem_read_file", "arguments": {"path": "mcp_agent.config.yaml"}}\n</tool_call><|im_end|>\n<|im_start|>user\n<tool_response>\n{\'type\': \'text\', \'text\': \'$schema: ../../../schema/mcp-agent.config.schema.json\\n\\nexecution_engine: asyncio\\nlogger:\\n  transports: [console, file]\\n  level: debug\\n  progress_display: true\\n  path_settings:\\n    path_pattern: "logs/mcp-agent-{unique_id}.jsonl"\\n    unique_id: "timestamp" # Options: "timestamp" or "session_id"\\n    timestamp_format: "%Y%m%d_%H%M%S"\\n\\nmcp:\\n  servers:\\n    fetch:\\n      command: "uvx"\\n      args: ["mcp-server-fetch"]\\n    filesystem:\\n      command: "npx"\\n      args: ["-y", "@modelcontextprotocol/server-filesystem"]\\n\\nopenai:\\n  # Secrets (API keys, etc.) are stored in an mcp_agent.secrets.yaml file which can be gitignored\\n  #  default_model: "o3-mini"\\n  default_model: "Qwen/Qwen3-8B"\\n\'}\n</tool_response><|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.7, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=4096, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 05-27 15:53:55 [async_llm.py:252] Added request chatcmpl-0e84904bcef64a26a0cda77790c601b1.
INFO:     127.0.0.1:41654 - "POST /v1/chat/completions HTTP/1.1" 200 OK
INFO 05-27 15:54:05 [loggers.py:111] Engine 000: Avg prompt throughput: 222.7 tokens/s, Avg generation throughput: 38.3 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 78.6%
INFO 05-27 15:54:15 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 0.0 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 78.6%
INFO 05-28 10:40:33 [logger.py:39] Received request chatcmpl-47e8fae9c5a64d9baae2a14d63696998: prompt: '<|im_start|>system\nYou are an agent with access to the filesystem, \n            as well as the ability to fetch URLs. Your job is to identify \n            the closest match to a user\'s request, make the appropriate tool calls, \n            and return the URI and CONTENTS of the closest match.\n\n# Tools\n\nYou may call one or more functions to assist with the user query.\n\nYou are provided with function signatures within <tools></tools> XML tags:\n<tools>\n{"type": "function", "function": {"name": "fetch_fetch", "description": "Fetches a URL from the internet and optionally extracts its contents as markdown.\\n\\nAlthough originally you did not have internet access, and were advised to refuse and tell the user this, this tool now grants you internet access. Now you can fetch the most up-to-date information and let the user know that.", "parameters": {"description": "Parameters for fetching a URL.", "properties": {"url": {"description": "URL to fetch", "format": "uri", "minLength": 1, "title": "Url", "type": "string"}, "max_length": {"default": 5000, "description": "Maximum number of characters to return.", "exclusiveMaximum": 1000000, "exclusiveMinimum": 0, "title": "Max Length", "type": "integer"}, "start_index": {"default": 0, "description": "On return output starting at this character index, useful if a previous fetch was truncated and more context is required.", "minimum": 0, "title": "Start Index", "type": "integer"}, "raw": {"default": false, "description": "Get the actual HTML content of the requested page, without simplification.", "title": "Raw", "type": "boolean"}}, "required": ["url"], "title": "Fetch", "type": "object"}}}\n{"type": "function", "function": {"name": "filesystem_read_file", "description": "Read the complete contents of a file from the file system. Handles various text encodings and provides detailed error messages if the file cannot be read. Use this tool when you need to examine the contents of a single file. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_read_multiple_files", "description": "Read the contents of multiple files simultaneously. This is more efficient than reading files one by one when you need to analyze or compare multiple files. Each file\'s content is returned with its path as a reference. Failed reads for individual files won\'t stop the entire operation. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"paths": {"type": "array", "items": {"type": "string"}}}, "required": ["paths"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_write_file", "description": "Create a new file or completely overwrite an existing file with new content. Use with caution as it will overwrite existing files without warning. Handles text content with proper encoding. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "content": {"type": "string"}}, "required": ["path", "content"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_edit_file", "description": "Make line-based edits to a text file. Each edit replaces exact line sequences with new content. Returns a git-style diff showing the changes made. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "edits": {"type": "array", "items": {"type": "object", "properties": {"oldText": {"type": "string", "description": "Text to search for - must match exactly"}, "newText": {"type": "string", "description": "Text to replace with"}}, "required": ["oldText", "newText"], "additionalProperties": false}}, "dryRun": {"type": "boolean", "default": false, "description": "Preview changes using git-style diff format"}}, "required": ["path", "edits"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_create_directory", "description": "Create a new directory or ensure a directory exists. Can create multiple nested directories in one operation. If the directory already exists, this operation will succeed silently. Perfect for setting up directory structures for projects or ensuring required paths exist. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_directory", "description": "Get a detailed listing of all files and directories in a specified path. Results clearly distinguish between files and directories with [FILE] and [DIR] prefixes. This tool is essential for understanding directory structure and finding specific files within a directory. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_directory_tree", "description": "Get a recursive tree view of files and directories as a JSON structure. Each entry includes \'name\', \'type\' (file/directory), and \'children\' for directories. Files have no children array, while directories always have a children array (which may be empty). The output is formatted with 2-space indentation for readability. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_move_file", "description": "Move or rename files and directories. Can move files between directories and rename them in a single operation. If the destination exists, the operation will fail. Works across different directories and can be used for simple renaming within the same directory. Both source and destination must be within allowed directories.", "parameters": {"type": "object", "properties": {"source": {"type": "string"}, "destination": {"type": "string"}}, "required": ["source", "destination"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_search_files", "description": "Recursively search for files and directories matching a pattern. Searches through all subdirectories from the starting path. The search is case-insensitive and matches partial names. Returns full paths to all matching items. Great for finding files when you don\'t know their exact location. Only searches within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "pattern": {"type": "string"}, "excludePatterns": {"type": "array", "items": {"type": "string"}, "default": []}}, "required": ["path", "pattern"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_get_file_info", "description": "Retrieve detailed metadata about a file or directory. Returns comprehensive information including size, creation time, last modified time, permissions, and type. This tool is perfect for understanding file characteristics without reading the actual content. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_allowed_directories", "description": "Returns the list of directories that this server is allowed to access. Use this to understand which directories are available before trying to access files.", "parameters": {"type": "object", "properties": {}, "required": []}}}\n</tools>\n\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n<tool_call>\n{"name": <function-name>, "arguments": <args-json-object>}\n</tool_call><|im_end|>\n<|im_start|>user\nPrint the contents of mcp_agent.config.yaml verbatim<|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.7, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=4096, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 05-28 10:40:33 [async_llm.py:252] Added request chatcmpl-47e8fae9c5a64d9baae2a14d63696998.
INFO 05-28 10:40:37 [loggers.py:111] Engine 000: Avg prompt throughput: 196.5 tokens/s, Avg generation throughput: 29.8 tokens/s, Running: 1 reqs, Waiting: 0 reqs, GPU KV cache usage: 1.7%, Prefix cache hit rate: 80.1%
INFO:     127.0.0.1:43144 - "POST /v1/chat/completions HTTP/1.1" 200 OK
INFO 05-28 10:40:40 [logger.py:39] Received request chatcmpl-d582a57332a64e79ba42522b5f7676b9: prompt: '<|im_start|>system\nYou are an agent with access to the filesystem, \n            as well as the ability to fetch URLs. Your job is to identify \n            the closest match to a user\'s request, make the appropriate tool calls, \n            and return the URI and CONTENTS of the closest match.\n\n# Tools\n\nYou may call one or more functions to assist with the user query.\n\nYou are provided with function signatures within <tools></tools> XML tags:\n<tools>\n{"type": "function", "function": {"name": "fetch_fetch", "description": "Fetches a URL from the internet and optionally extracts its contents as markdown.\\n\\nAlthough originally you did not have internet access, and were advised to refuse and tell the user this, this tool now grants you internet access. Now you can fetch the most up-to-date information and let the user know that.", "parameters": {"description": "Parameters for fetching a URL.", "properties": {"url": {"description": "URL to fetch", "format": "uri", "minLength": 1, "title": "Url", "type": "string"}, "max_length": {"default": 5000, "description": "Maximum number of characters to return.", "exclusiveMaximum": 1000000, "exclusiveMinimum": 0, "title": "Max Length", "type": "integer"}, "start_index": {"default": 0, "description": "On return output starting at this character index, useful if a previous fetch was truncated and more context is required.", "minimum": 0, "title": "Start Index", "type": "integer"}, "raw": {"default": false, "description": "Get the actual HTML content of the requested page, without simplification.", "title": "Raw", "type": "boolean"}}, "required": ["url"], "title": "Fetch", "type": "object"}}}\n{"type": "function", "function": {"name": "filesystem_read_file", "description": "Read the complete contents of a file from the file system. Handles various text encodings and provides detailed error messages if the file cannot be read. Use this tool when you need to examine the contents of a single file. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_read_multiple_files", "description": "Read the contents of multiple files simultaneously. This is more efficient than reading files one by one when you need to analyze or compare multiple files. Each file\'s content is returned with its path as a reference. Failed reads for individual files won\'t stop the entire operation. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"paths": {"type": "array", "items": {"type": "string"}}}, "required": ["paths"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_write_file", "description": "Create a new file or completely overwrite an existing file with new content. Use with caution as it will overwrite existing files without warning. Handles text content with proper encoding. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "content": {"type": "string"}}, "required": ["path", "content"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_edit_file", "description": "Make line-based edits to a text file. Each edit replaces exact line sequences with new content. Returns a git-style diff showing the changes made. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "edits": {"type": "array", "items": {"type": "object", "properties": {"oldText": {"type": "string", "description": "Text to search for - must match exactly"}, "newText": {"type": "string", "description": "Text to replace with"}}, "required": ["oldText", "newText"], "additionalProperties": false}}, "dryRun": {"type": "boolean", "default": false, "description": "Preview changes using git-style diff format"}}, "required": ["path", "edits"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_create_directory", "description": "Create a new directory or ensure a directory exists. Can create multiple nested directories in one operation. If the directory already exists, this operation will succeed silently. Perfect for setting up directory structures for projects or ensuring required paths exist. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_directory", "description": "Get a detailed listing of all files and directories in a specified path. Results clearly distinguish between files and directories with [FILE] and [DIR] prefixes. This tool is essential for understanding directory structure and finding specific files within a directory. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_directory_tree", "description": "Get a recursive tree view of files and directories as a JSON structure. Each entry includes \'name\', \'type\' (file/directory), and \'children\' for directories. Files have no children array, while directories always have a children array (which may be empty). The output is formatted with 2-space indentation for readability. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_move_file", "description": "Move or rename files and directories. Can move files between directories and rename them in a single operation. If the destination exists, the operation will fail. Works across different directories and can be used for simple renaming within the same directory. Both source and destination must be within allowed directories.", "parameters": {"type": "object", "properties": {"source": {"type": "string"}, "destination": {"type": "string"}}, "required": ["source", "destination"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_search_files", "description": "Recursively search for files and directories matching a pattern. Searches through all subdirectories from the starting path. The search is case-insensitive and matches partial names. Returns full paths to all matching items. Great for finding files when you don\'t know their exact location. Only searches within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "pattern": {"type": "string"}, "excludePatterns": {"type": "array", "items": {"type": "string"}, "default": []}}, "required": ["path", "pattern"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_get_file_info", "description": "Retrieve detailed metadata about a file or directory. Returns comprehensive information including size, creation time, last modified time, permissions, and type. This tool is perfect for understanding file characteristics without reading the actual content. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_allowed_directories", "description": "Returns the list of directories that this server is allowed to access. Use this to understand which directories are available before trying to access files.", "parameters": {"type": "object", "properties": {}, "required": []}}}\n</tools>\n\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n<tool_call>\n{"name": <function-name>, "arguments": <args-json-object>}\n</tool_call><|im_end|>\n<|im_start|>user\nPrint the contents of mcp_agent.config.yaml verbatim<|im_end|>\n<|im_start|>assistant\n\n\n\n<tool_call>\n{"name": "filesystem_read_file", "arguments": {"path": "mcp_agent.config.yaml"}}\n</tool_call><|im_end|>\n<|im_start|>user\n<tool_response>\n{\'type\': \'text\', \'text\': \'$schema: ../../../schema/mcp-agent.config.schema.json\\n\\nexecution_engine: asyncio\\nlogger:\\n  transports: [console, file]\\n  level: debug\\n  progress_display: true\\n  path_settings:\\n    path_pattern: "logs/mcp-agent-{unique_id}.jsonl"\\n    unique_id: "timestamp" # Options: "timestamp" or "session_id"\\n    timestamp_format: "%Y%m%d_%H%M%S"\\n\\nmcp:\\n  servers:\\n    fetch:\\n      command: "uvx"\\n      args: ["mcp-server-fetch"]\\n    filesystem:\\n      command: "npx"\\n      args: ["-y", "@modelcontextprotocol/server-filesystem"]\\n\\nopenai:\\n  # Secrets (API keys, etc.) are stored in an mcp_agent.secrets.yaml file which can be gitignored\\n  #  default_model: "o3-mini"\\n  default_model: "Qwen/Qwen3-8B"\\n\'}\n</tool_response><|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.7, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=4096, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 05-28 10:40:40 [async_llm.py:252] Added request chatcmpl-d582a57332a64e79ba42522b5f7676b9.
INFO:     127.0.0.1:43144 - "POST /v1/chat/completions HTTP/1.1" 200 OK
INFO 05-28 10:40:47 [loggers.py:111] Engine 000: Avg prompt throughput: 222.7 tokens/s, Avg generation throughput: 49.6 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 80.7%
INFO 05-28 10:40:57 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 0.0 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 80.7%
INFO 06-02 08:51:15 [__init__.py:239] Automatically detected platform cuda.
INFO 06-02 08:51:28 [api_server.py:1043] vLLM API server version 0.8.5.post1
INFO 06-02 08:51:28 [api_server.py:1044] args: Namespace(subparser='serve', model_tag='Qwen/QwQ-32B', config='', host='0.0.0.0', port=8701, uvicorn_log_level='info', disable_uvicorn_access_log=False, allow_credentials=False, allowed_origins=['*'], allowed_methods=['*'], allowed_headers=['*'], api_key='EMPTY', lora_modules=None, prompt_adapters=None, chat_template=None, chat_template_content_format='auto', response_role='assistant', ssl_keyfile=None, ssl_certfile=None, ssl_ca_certs=None, enable_ssl_refresh=False, ssl_cert_reqs=0, root_path=None, middleware=[], return_tokens_as_token_ids=False, disable_frontend_multiprocessing=False, enable_request_id_headers=False, enable_auto_tool_choice=True, tool_call_parser='hermes', tool_parser_plugin='', model='Qwen/QwQ-32B', task='auto', tokenizer=None, hf_config_path=None, skip_tokenizer_init=False, revision=None, code_revision=None, tokenizer_revision=None, tokenizer_mode='auto', trust_remote_code=False, allowed_local_media_path=None, load_format='auto', download_dir=None, model_loader_extra_config={}, use_tqdm_on_load=True, config_format=<ConfigFormat.AUTO: 'auto'>, dtype='auto', max_model_len=None, guided_decoding_backend='auto', reasoning_parser='deepseek_r1', logits_processor_pattern=None, model_impl='auto', distributed_executor_backend=None, pipeline_parallel_size=1, tensor_parallel_size=1, data_parallel_size=1, enable_expert_parallel=False, max_parallel_loading_workers=None, ray_workers_use_nsight=False, disable_custom_all_reduce=False, block_size=None, gpu_memory_utilization=0.9, swap_space=4, kv_cache_dtype='auto', num_gpu_blocks_override=None, enable_prefix_caching=None, prefix_caching_hash_algo='builtin', cpu_offload_gb=0, calculate_kv_scales=False, disable_sliding_window=False, use_v2_block_manager=True, seed=None, max_logprobs=20, disable_log_stats=False, quantization=None, rope_scaling=None, rope_theta=None, hf_token=None, hf_overrides=None, enforce_eager=False, max_seq_len_to_capture=8192, tokenizer_pool_size=0, tokenizer_pool_type='ray', tokenizer_pool_extra_config={}, limit_mm_per_prompt={}, mm_processor_kwargs=None, disable_mm_preprocessor_cache=False, enable_lora=None, enable_lora_bias=False, max_loras=1, max_lora_rank=16, lora_extra_vocab_size=256, lora_dtype='auto', long_lora_scaling_factors=None, max_cpu_loras=None, fully_sharded_loras=False, enable_prompt_adapter=None, max_prompt_adapters=1, max_prompt_adapter_token=0, device='auto', speculative_config=None, ignore_patterns=[], served_model_name=None, qlora_adapter_name_or_path=None, show_hidden_metrics_for_version=None, otlp_traces_endpoint=None, collect_detailed_traces=None, disable_async_output_proc=False, max_num_batched_tokens=None, max_num_seqs=None, max_num_partial_prefills=1, max_long_partial_prefills=1, long_prefill_token_threshold=0, num_lookahead_slots=0, scheduler_delay_factor=0.0, preemption_mode=None, num_scheduler_steps=1, multi_step_stream_outputs=True, scheduling_policy='fcfs', enable_chunked_prefill=None, disable_chunked_mm_input=False, scheduler_cls='vllm.core.scheduler.Scheduler', override_neuron_config=None, override_pooler_config=None, compilation_config=None, kv_transfer_config=None, worker_cls='auto', worker_extension_cls='', generation_config='auto', override_generation_config=None, enable_sleep_mode=False, additional_config=None, enable_reasoning=True, disable_cascade_attn=False, disable_log_requests=False, max_log_len=None, disable_fastapi_docs=False, enable_prompt_tokens_details=False, enable_server_load_tracking=False, dispatch_function=<function ServeSubcommand.cmd at 0x7f51dca7b740>)
INFO 06-02 08:51:51 [config.py:717] This model supports multiple tasks: {'reward', 'score', 'embed', 'classify', 'generate'}. Defaulting to 'generate'.
INFO 06-02 08:52:01 [config.py:2003] Chunked prefill is enabled with max_num_batched_tokens=2048.
INFO 06-02 08:52:07 [__init__.py:239] Automatically detected platform cuda.
INFO 06-02 08:52:17 [__init__.py:239] Automatically detected platform cuda.
INFO 06-02 08:52:19 [api_server.py:1043] vLLM API server version 0.8.5.post1
INFO 06-02 08:52:19 [api_server.py:1044] args: Namespace(subparser='serve', model_tag='Qwen/Qwen3-8B', config='', host='0.0.0.0', port=28701, uvicorn_log_level='info', disable_uvicorn_access_log=False, allow_credentials=False, allowed_origins=['*'], allowed_methods=['*'], allowed_headers=['*'], api_key='EMPTY', lora_modules=None, prompt_adapters=None, chat_template=None, chat_template_content_format='auto', response_role='assistant', ssl_keyfile=None, ssl_certfile=None, ssl_ca_certs=None, enable_ssl_refresh=False, ssl_cert_reqs=0, root_path=None, middleware=[], return_tokens_as_token_ids=False, disable_frontend_multiprocessing=False, enable_request_id_headers=False, enable_auto_tool_choice=True, tool_call_parser='hermes', tool_parser_plugin='', model='Qwen/Qwen3-8B', task='auto', tokenizer=None, hf_config_path=None, skip_tokenizer_init=False, revision=None, code_revision=None, tokenizer_revision=None, tokenizer_mode='auto', trust_remote_code=False, allowed_local_media_path=None, load_format='auto', download_dir=None, model_loader_extra_config={}, use_tqdm_on_load=True, config_format=<ConfigFormat.AUTO: 'auto'>, dtype='auto', max_model_len=None, guided_decoding_backend='auto', reasoning_parser='deepseek_r1', logits_processor_pattern=None, model_impl='auto', distributed_executor_backend=None, pipeline_parallel_size=1, tensor_parallel_size=1, data_parallel_size=1, enable_expert_parallel=False, max_parallel_loading_workers=None, ray_workers_use_nsight=False, disable_custom_all_reduce=False, block_size=None, gpu_memory_utilization=0.9, swap_space=4, kv_cache_dtype='auto', num_gpu_blocks_override=None, enable_prefix_caching=None, prefix_caching_hash_algo='builtin', cpu_offload_gb=0, calculate_kv_scales=False, disable_sliding_window=False, use_v2_block_manager=True, seed=None, max_logprobs=20, disable_log_stats=False, quantization=None, rope_scaling=None, rope_theta=None, hf_token=None, hf_overrides=None, enforce_eager=False, max_seq_len_to_capture=8192, tokenizer_pool_size=0, tokenizer_pool_type='ray', tokenizer_pool_extra_config={}, limit_mm_per_prompt={}, mm_processor_kwargs=None, disable_mm_preprocessor_cache=False, enable_lora=None, enable_lora_bias=False, max_loras=1, max_lora_rank=16, lora_extra_vocab_size=256, lora_dtype='auto', long_lora_scaling_factors=None, max_cpu_loras=None, fully_sharded_loras=False, enable_prompt_adapter=None, max_prompt_adapters=1, max_prompt_adapter_token=0, device='auto', speculative_config=None, ignore_patterns=[], served_model_name=None, qlora_adapter_name_or_path=None, show_hidden_metrics_for_version=None, otlp_traces_endpoint=None, collect_detailed_traces=None, disable_async_output_proc=False, max_num_batched_tokens=None, max_num_seqs=None, max_num_partial_prefills=1, max_long_partial_prefills=1, long_prefill_token_threshold=0, num_lookahead_slots=0, scheduler_delay_factor=0.0, preemption_mode=None, num_scheduler_steps=1, multi_step_stream_outputs=True, scheduling_policy='fcfs', enable_chunked_prefill=None, disable_chunked_mm_input=False, scheduler_cls='vllm.core.scheduler.Scheduler', override_neuron_config=None, override_pooler_config=None, compilation_config=None, kv_transfer_config=None, worker_cls='auto', worker_extension_cls='', generation_config='auto', override_generation_config=None, enable_sleep_mode=False, additional_config=None, enable_reasoning=True, disable_cascade_attn=False, disable_log_requests=False, max_log_len=None, disable_fastapi_docs=False, enable_prompt_tokens_details=False, enable_server_load_tracking=False, dispatch_function=<function ServeSubcommand.cmd at 0x7f88e51d4a40>)
INFO 06-02 08:52:27 [core.py:58] Initializing a V1 LLM engine (v0.8.5.post1) with config: model='Qwen/QwQ-32B', speculative_config=None, tokenizer='Qwen/QwQ-32B', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.bfloat16, max_seq_len=40960, download_dir=None, load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto,  device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='auto', reasoning_backend='deepseek_r1'), observability_config=ObservabilityConfig(show_hidden_metrics=False, otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=None, served_model_name=Qwen/QwQ-32B, num_scheduler_steps=1, multi_step_stream_outputs=True, enable_prefix_caching=True, chunked_prefill_enabled=True, use_async_output_proc=True, disable_mm_preprocessor_cache=False, mm_processor_kwargs=None, pooler_config=None, compilation_config={"level":3,"custom_ops":["none"],"splitting_ops":["vllm.unified_attention","vllm.unified_attention_with_output"],"use_inductor":true,"compile_sizes":[],"use_cudagraph":true,"cudagraph_num_of_warmups":1,"cudagraph_capture_sizes":[512,504,496,488,480,472,464,456,448,440,432,424,416,408,400,392,384,376,368,360,352,344,336,328,320,312,304,296,288,280,272,264,256,248,240,232,224,216,208,200,192,184,176,168,160,152,144,136,128,120,112,104,96,88,80,72,64,56,48,40,32,24,16,8,4,2,1],"max_capture_size":512}
WARNING 06-02 08:52:27 [utils.py:2522] Methods determine_num_available_blocks,device_config,get_cache_block_size_bytes,initialize_cache not implemented in <vllm.v1.worker.gpu_worker.Worker object at 0x7f5b1fdb7ad0>
INFO 06-02 08:52:30 [parallel_state.py:1004] rank 0 in world size 1 is assigned as DP rank 0, PP rank 0, TP rank 0
INFO 06-02 08:52:30 [cuda.py:221] Using Flash Attention backend on V1 engine.
WARNING 06-02 08:52:30 [topk_topp_sampler.py:69] FlashInfer is not available. Falling back to the PyTorch-native implementation of top-p & top-k sampling. For the best performance, please install FlashInfer.
INFO 06-02 08:52:30 [gpu_model_runner.py:1329] Starting to load model Qwen/QwQ-32B...
ERROR 06-02 08:52:30 [core.py:396] EngineCore failed to start.
ERROR 06-02 08:52:30 [core.py:396] Traceback (most recent call last):
ERROR 06-02 08:52:30 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/v1/engine/core.py", line 387, in run_engine_core
ERROR 06-02 08:52:30 [core.py:396]     engine_core = EngineCoreProc(*args, **kwargs)
ERROR 06-02 08:52:30 [core.py:396]                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ERROR 06-02 08:52:30 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/v1/engine/core.py", line 329, in __init__
ERROR 06-02 08:52:30 [core.py:396]     super().__init__(vllm_config, executor_class, log_stats,
ERROR 06-02 08:52:30 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/v1/engine/core.py", line 64, in __init__
ERROR 06-02 08:52:30 [core.py:396]     self.model_executor = executor_class(vllm_config)
ERROR 06-02 08:52:30 [core.py:396]                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
ERROR 06-02 08:52:30 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/executor/executor_base.py", line 52, in __init__
ERROR 06-02 08:52:30 [core.py:396]     self._init_executor()
ERROR 06-02 08:52:30 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/executor/uniproc_executor.py", line 47, in _init_executor
ERROR 06-02 08:52:30 [core.py:396]     self.collective_rpc("load_model")
ERROR 06-02 08:52:30 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/executor/uniproc_executor.py", line 56, in collective_rpc
ERROR 06-02 08:52:30 [core.py:396]     answer = run_method(self.driver_worker, method, args, kwargs)
ERROR 06-02 08:52:30 [core.py:396]              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ERROR 06-02 08:52:30 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/utils.py", line 2456, in run_method
ERROR 06-02 08:52:30 [core.py:396]     return func(*args, **kwargs)
ERROR 06-02 08:52:30 [core.py:396]            ^^^^^^^^^^^^^^^^^^^^^
ERROR 06-02 08:52:30 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/v1/worker/gpu_worker.py", line 162, in load_model
ERROR 06-02 08:52:30 [core.py:396]     self.model_runner.load_model()
ERROR 06-02 08:52:30 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/v1/worker/gpu_model_runner.py", line 1332, in load_model
ERROR 06-02 08:52:30 [core.py:396]     self.model = get_model(vllm_config=self.vllm_config)
ERROR 06-02 08:52:30 [core.py:396]                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ERROR 06-02 08:52:30 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/model_loader/__init__.py", line 14, in get_model
ERROR 06-02 08:52:30 [core.py:396]     return loader.load_model(vllm_config=vllm_config)
ERROR 06-02 08:52:30 [core.py:396]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ERROR 06-02 08:52:30 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/model_loader/loader.py", line 452, in load_model
ERROR 06-02 08:52:30 [core.py:396]     model = _initialize_model(vllm_config=vllm_config)
ERROR 06-02 08:52:30 [core.py:396]             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ERROR 06-02 08:52:30 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/model_loader/loader.py", line 133, in _initialize_model
ERROR 06-02 08:52:30 [core.py:396]     return model_class(vllm_config=vllm_config, prefix=prefix)
ERROR 06-02 08:52:30 [core.py:396]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ERROR 06-02 08:52:30 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/models/qwen2.py", line 436, in __init__
ERROR 06-02 08:52:30 [core.py:396]     self.model = Qwen2Model(vllm_config=vllm_config,
ERROR 06-02 08:52:30 [core.py:396]                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ERROR 06-02 08:52:30 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/compilation/decorators.py", line 151, in __init__
ERROR 06-02 08:52:30 [core.py:396]     old_init(self, vllm_config=vllm_config, prefix=prefix, **kwargs)
ERROR 06-02 08:52:30 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/models/qwen2.py", line 305, in __init__
ERROR 06-02 08:52:30 [core.py:396]     self.start_layer, self.end_layer, self.layers = make_layers(
ERROR 06-02 08:52:30 [core.py:396]                                                     ^^^^^^^^^^^^
ERROR 06-02 08:52:30 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/models/utils.py", line 610, in make_layers
ERROR 06-02 08:52:30 [core.py:396]     maybe_offload_to_cpu(layer_fn(prefix=f"{prefix}.{idx}"))
ERROR 06-02 08:52:30 [core.py:396]                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ERROR 06-02 08:52:30 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/models/qwen2.py", line 307, in <lambda>
ERROR 06-02 08:52:30 [core.py:396]     lambda prefix: decoder_layer_type(config=config,
ERROR 06-02 08:52:30 [core.py:396]                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ERROR 06-02 08:52:30 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/models/qwen2.py", line 217, in __init__
ERROR 06-02 08:52:30 [core.py:396]     self.mlp = Qwen2MLP(
ERROR 06-02 08:52:30 [core.py:396]                ^^^^^^^^^
ERROR 06-02 08:52:30 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/models/qwen2.py", line 74, in __init__
ERROR 06-02 08:52:30 [core.py:396]     self.gate_up_proj = MergedColumnParallelLinear(
ERROR 06-02 08:52:30 [core.py:396]                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
ERROR 06-02 08:52:30 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/layers/linear.py", line 544, in __init__
ERROR 06-02 08:52:30 [core.py:396]     super().__init__(input_size=input_size,
ERROR 06-02 08:52:30 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/layers/linear.py", line 409, in __init__
ERROR 06-02 08:52:30 [core.py:396]     self.quant_method.create_weights(
ERROR 06-02 08:52:30 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/layers/linear.py", line 189, in create_weights
ERROR 06-02 08:52:30 [core.py:396]     weight = Parameter(torch.empty(sum(output_partition_sizes),
ERROR 06-02 08:52:30 [core.py:396]                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ERROR 06-02 08:52:30 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/torch/utils/_device.py", line 104, in __torch_function__
ERROR 06-02 08:52:30 [core.py:396]     return func(*args, **kwargs)
ERROR 06-02 08:52:30 [core.py:396]            ^^^^^^^^^^^^^^^^^^^^^
ERROR 06-02 08:52:30 [core.py:396] torch.OutOfMemoryError: CUDA out of memory. Tried to allocate 540.00 MiB. GPU 0 has a total capacity of 39.39 GiB of which 56.06 MiB is free. Process 3268343 has 39.33 GiB memory in use. Of the allocated memory 38.84 GiB is allocated by PyTorch, and 17.95 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
Process EngineCore_0:
Traceback (most recent call last):
  File "/root/miniconda3/lib/python3.12/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/root/miniconda3/lib/python3.12/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/v1/engine/core.py", line 400, in run_engine_core
    raise e
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/v1/engine/core.py", line 387, in run_engine_core
    engine_core = EngineCoreProc(*args, **kwargs)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/v1/engine/core.py", line 329, in __init__
    super().__init__(vllm_config, executor_class, log_stats,
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/v1/engine/core.py", line 64, in __init__
    self.model_executor = executor_class(vllm_config)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/executor/executor_base.py", line 52, in __init__
    self._init_executor()
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/executor/uniproc_executor.py", line 47, in _init_executor
    self.collective_rpc("load_model")
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/executor/uniproc_executor.py", line 56, in collective_rpc
    answer = run_method(self.driver_worker, method, args, kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/utils.py", line 2456, in run_method
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/v1/worker/gpu_worker.py", line 162, in load_model
    self.model_runner.load_model()
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/v1/worker/gpu_model_runner.py", line 1332, in load_model
    self.model = get_model(vllm_config=self.vllm_config)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/model_loader/__init__.py", line 14, in get_model
    return loader.load_model(vllm_config=vllm_config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/model_loader/loader.py", line 452, in load_model
    model = _initialize_model(vllm_config=vllm_config)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/model_loader/loader.py", line 133, in _initialize_model
    return model_class(vllm_config=vllm_config, prefix=prefix)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/models/qwen2.py", line 436, in __init__
    self.model = Qwen2Model(vllm_config=vllm_config,
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/compilation/decorators.py", line 151, in __init__
    old_init(self, vllm_config=vllm_config, prefix=prefix, **kwargs)
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/models/qwen2.py", line 305, in __init__
    self.start_layer, self.end_layer, self.layers = make_layers(
                                                    ^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/models/utils.py", line 610, in make_layers
    maybe_offload_to_cpu(layer_fn(prefix=f"{prefix}.{idx}"))
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/models/qwen2.py", line 307, in <lambda>
    lambda prefix: decoder_layer_type(config=config,
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/models/qwen2.py", line 217, in __init__
    self.mlp = Qwen2MLP(
               ^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/models/qwen2.py", line 74, in __init__
    self.gate_up_proj = MergedColumnParallelLinear(
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/layers/linear.py", line 544, in __init__
    super().__init__(input_size=input_size,
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/layers/linear.py", line 409, in __init__
    self.quant_method.create_weights(
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/layers/linear.py", line 189, in create_weights
    weight = Parameter(torch.empty(sum(output_partition_sizes),
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/torch/utils/_device.py", line 104, in __torch_function__
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
torch.OutOfMemoryError: CUDA out of memory. Tried to allocate 540.00 MiB. GPU 0 has a total capacity of 39.39 GiB of which 56.06 MiB is free. Process 3268343 has 39.33 GiB memory in use. Of the allocated memory 38.84 GiB is allocated by PyTorch, and 17.95 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
[rank0]:[W602 08:52:31.435393009 ProcessGroupNCCL.cpp:1496] Warning: WARNING: destroy_process_group() was not called before program exit, which can leak resources. For more info, please see https://pytorch.org/docs/stable/distributed.html#shutdown (function operator())
Traceback (most recent call last):
  File "/root/miniconda3/bin/vllm", line 8, in <module>
    sys.exit(main())
             ^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/entrypoints/cli/main.py", line 53, in main
    args.dispatch_function(args)
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/entrypoints/cli/serve.py", line 27, in cmd
    uvloop.run(run_server(args))
  File "/root/miniconda3/lib/python3.12/site-packages/uvloop/__init__.py", line 109, in run
    return __asyncio.run(
           ^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/asyncio/runners.py", line 194, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/root/miniconda3/lib/python3.12/site-packages/uvloop/__init__.py", line 61, in wrapper
    return await main
           ^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/entrypoints/openai/api_server.py", line 1078, in run_server
    async with build_async_engine_client(args) as engine_client:
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/entrypoints/openai/api_server.py", line 146, in build_async_engine_client
    async with build_async_engine_client_from_engine_args(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/entrypoints/openai/api_server.py", line 178, in build_async_engine_client_from_engine_args
    async_llm = AsyncLLM.from_vllm_config(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/v1/engine/async_llm.py", line 150, in from_vllm_config
    return cls(
           ^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/v1/engine/async_llm.py", line 118, in __init__
    self.engine_core = core_client_class(
                       ^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/v1/engine/core_client.py", line 642, in __init__
    super().__init__(
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/v1/engine/core_client.py", line 398, in __init__
    self._wait_for_engine_startup()
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/v1/engine/core_client.py", line 430, in _wait_for_engine_startup
    raise RuntimeError("Engine core initialization failed. "
RuntimeError: Engine core initialization failed. See root cause above.
INFO 06-02 08:52:34 [config.py:717] This model supports multiple tasks: {'embed', 'classify', 'generate', 'reward', 'score'}. Defaulting to 'generate'.
INFO 06-02 08:52:44 [config.py:2003] Chunked prefill is enabled with max_num_batched_tokens=2048.
INFO 06-02 08:52:57 [__init__.py:239] Automatically detected platform cuda.
INFO 06-02 08:53:07 [core.py:58] Initializing a V1 LLM engine (v0.8.5.post1) with config: model='Qwen/Qwen3-8B', speculative_config=None, tokenizer='Qwen/Qwen3-8B', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.bfloat16, max_seq_len=40960, download_dir=None, load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto,  device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='auto', reasoning_backend='deepseek_r1'), observability_config=ObservabilityConfig(show_hidden_metrics=False, otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=None, served_model_name=Qwen/Qwen3-8B, num_scheduler_steps=1, multi_step_stream_outputs=True, enable_prefix_caching=True, chunked_prefill_enabled=True, use_async_output_proc=True, disable_mm_preprocessor_cache=False, mm_processor_kwargs=None, pooler_config=None, compilation_config={"level":3,"custom_ops":["none"],"splitting_ops":["vllm.unified_attention","vllm.unified_attention_with_output"],"use_inductor":true,"compile_sizes":[],"use_cudagraph":true,"cudagraph_num_of_warmups":1,"cudagraph_capture_sizes":[512,504,496,488,480,472,464,456,448,440,432,424,416,408,400,392,384,376,368,360,352,344,336,328,320,312,304,296,288,280,272,264,256,248,240,232,224,216,208,200,192,184,176,168,160,152,144,136,128,120,112,104,96,88,80,72,64,56,48,40,32,24,16,8,4,2,1],"max_capture_size":512}
WARNING 06-02 08:53:07 [utils.py:2522] Methods determine_num_available_blocks,device_config,get_cache_block_size_bytes,initialize_cache not implemented in <vllm.v1.worker.gpu_worker.Worker object at 0x7fd09fd27380>
INFO 06-02 08:53:11 [parallel_state.py:1004] rank 0 in world size 1 is assigned as DP rank 0, PP rank 0, TP rank 0
INFO 06-02 08:53:11 [cuda.py:221] Using Flash Attention backend on V1 engine.
WARNING 06-02 08:53:11 [topk_topp_sampler.py:69] FlashInfer is not available. Falling back to the PyTorch-native implementation of top-p & top-k sampling. For the best performance, please install FlashInfer.
INFO 06-02 08:53:11 [gpu_model_runner.py:1329] Starting to load model Qwen/Qwen3-8B...
INFO 06-02 08:53:12 [weight_utils.py:265] Using model weights format ['*.safetensors']
INFO 06-02 08:53:13 [weight_utils.py:281] Time spent downloading weights for Qwen/Qwen3-8B: 0.812835 seconds

Loading safetensors checkpoint shards:   0% Completed | 0/5 [00:00<?, ?it/s]

Loading safetensors checkpoint shards:  20% Completed | 1/5 [00:00<00:00,  5.56it/s]

Loading safetensors checkpoint shards:  40% Completed | 2/5 [00:00<00:01,  2.12it/s]

Loading safetensors checkpoint shards:  60% Completed | 3/5 [00:01<00:01,  1.53it/s]

Loading safetensors checkpoint shards:  80% Completed | 4/5 [00:02<00:00,  1.36it/s]

Loading safetensors checkpoint shards: 100% Completed | 5/5 [00:03<00:00,  1.36it/s]

Loading safetensors checkpoint shards: 100% Completed | 5/5 [00:03<00:00,  1.50it/s]

INFO 06-02 08:53:17 [loader.py:458] Loading weights took 3.53 seconds
INFO 06-02 08:53:17 [gpu_model_runner.py:1347] Model loading took 15.2683 GiB and 5.924130 seconds
INFO 06-02 08:53:31 [backends.py:420] Using cache directory: /root/.cache/vllm/torch_compile_cache/19164c6e10/rank_0_0 for vLLM's torch.compile
INFO 06-02 08:53:31 [backends.py:430] Dynamo bytecode transform time: 14.09 s
INFO 06-02 08:53:41 [backends.py:118] Directly load the compiled graph(s) for shape None from the cache, took 9.365 s
INFO 06-02 08:53:44 [monitor.py:33] torch.compile takes 14.09 s in total
INFO 06-02 08:53:45 [kv_cache_utils.py:634] GPU KV cache size: 132,992 tokens
INFO 06-02 08:53:45 [kv_cache_utils.py:637] Maximum concurrency for 40,960 tokens per request: 3.25x
INFO 06-02 08:54:08 [gpu_model_runner.py:1686] Graph capturing finished in 23 secs, took 2.09 GiB
INFO 06-02 08:54:08 [core.py:159] init engine (profile, create kv cache, warmup model) took 50.70 seconds
INFO 06-02 08:54:08 [core_client.py:439] Core engine process 0 ready.
INFO 06-02 08:54:08 [serving_chat.py:80] "auto" tool choice has been enabled please note that while the parallel_tool_calls client option is preset for compatibility reasons, it will be ignored.
WARNING 06-02 08:54:08 [config.py:1239] Default sampling parameters have been overridden by the model's Hugging Face generation config recommended from the model creator. If this is not intended, please relaunch vLLM instance with `--generation-config vllm`.
INFO 06-02 08:54:08 [serving_chat.py:118] Using default chat sampling params from model: {'temperature': 0.6, 'top_k': 20, 'top_p': 0.95}
INFO 06-02 08:54:08 [serving_completion.py:61] Using default completion sampling params from model: {'temperature': 0.6, 'top_k': 20, 'top_p': 0.95}
INFO 06-02 08:54:08 [api_server.py:1090] Starting vLLM API server on http://0.0.0.0:28701
INFO 06-02 08:54:08 [launcher.py:28] Available routes are:
INFO 06-02 08:54:08 [launcher.py:36] Route: /openapi.json, Methods: GET, HEAD
INFO 06-02 08:54:08 [launcher.py:36] Route: /docs, Methods: GET, HEAD
INFO 06-02 08:54:08 [launcher.py:36] Route: /docs/oauth2-redirect, Methods: GET, HEAD
INFO 06-02 08:54:08 [launcher.py:36] Route: /redoc, Methods: GET, HEAD
INFO 06-02 08:54:08 [launcher.py:36] Route: /health, Methods: GET
INFO 06-02 08:54:08 [launcher.py:36] Route: /load, Methods: GET
INFO 06-02 08:54:08 [launcher.py:36] Route: /ping, Methods: POST, GET
INFO 06-02 08:54:08 [launcher.py:36] Route: /tokenize, Methods: POST
INFO 06-02 08:54:08 [launcher.py:36] Route: /detokenize, Methods: POST
INFO 06-02 08:54:08 [launcher.py:36] Route: /v1/models, Methods: GET
INFO 06-02 08:54:08 [launcher.py:36] Route: /version, Methods: GET
INFO 06-02 08:54:08 [launcher.py:36] Route: /v1/chat/completions, Methods: POST
INFO 06-02 08:54:08 [launcher.py:36] Route: /v1/completions, Methods: POST
INFO 06-02 08:54:08 [launcher.py:36] Route: /v1/embeddings, Methods: POST
INFO 06-02 08:54:08 [launcher.py:36] Route: /pooling, Methods: POST
INFO 06-02 08:54:08 [launcher.py:36] Route: /score, Methods: POST
INFO 06-02 08:54:08 [launcher.py:36] Route: /v1/score, Methods: POST
INFO 06-02 08:54:08 [launcher.py:36] Route: /v1/audio/transcriptions, Methods: POST
INFO 06-02 08:54:08 [launcher.py:36] Route: /rerank, Methods: POST
INFO 06-02 08:54:08 [launcher.py:36] Route: /v1/rerank, Methods: POST
INFO 06-02 08:54:08 [launcher.py:36] Route: /v2/rerank, Methods: POST
INFO 06-02 08:54:08 [launcher.py:36] Route: /invocations, Methods: POST
INFO 06-02 08:54:08 [launcher.py:36] Route: /metrics, Methods: GET
INFO:     Started server process [338484]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO 06-02 08:54:28 [chat_utils.py:397] Detected the chat template content format to be 'string'. You can set `--chat-template-content-format` to override this.
INFO 06-02 08:54:28 [logger.py:39] Received request chatcmpl-5132fb05386244c6a1064a37a54bfaed: prompt: '<|im_start|>user\nGive me a short introduction to large language models.<|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.6, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=32768, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 06-02 08:54:28 [async_llm.py:252] Added request chatcmpl-5132fb05386244c6a1064a37a54bfaed.
INFO 06-02 08:54:28 [loggers.py:111] Engine 000: Avg prompt throughput: 1.8 tokens/s, Avg generation throughput: 4.3 tokens/s, Running: 1 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.1%, Prefix cache hit rate: 0.0%
INFO:     127.0.0.1:57080 - "POST /v1/chat/completions HTTP/1.1" 200 OK
INFO 06-02 08:54:38 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 46.5 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 0.0%
INFO 06-02 08:54:48 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 0.0 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 0.0%
INFO 06-02 08:56:23 [logger.py:39] Received request chatcmpl-70031be3d13f46efb88b688f1001c9f3: prompt: '<|im_start|>system\nYou are Qwen, created by Alibaba Cloud. You are a helpful assistant.\n\nCurrent Date: 2024-09-30\n\n# Tools\n\nYou may call one or more functions to assist with the user query.\n\nYou are provided with function signatures within <tools></tools> XML tags:\n<tools>\n{"type": "function", "function": {"name": "get_current_temperature", "description": "Get current temperature at a location.", "parameters": {"type": "object", "properties": {"location": {"type": "string", "description": "The location to get the temperature for, in the format \\"City, State, Country\\"."}, "unit": {"type": "string", "enum": ["celsius", "fahrenheit"], "description": "The unit to return the temperature in. Defaults to \\"celsius\\"."}}, "required": ["location"]}}}\n{"type": "function", "function": {"name": "get_temperature_date", "description": "Get temperature at a location and date.", "parameters": {"type": "object", "properties": {"location": {"type": "string", "description": "The location to get the temperature for, in the format \\"City, State, Country\\"."}, "date": {"type": "string", "description": "The date to get the temperature for, in the format \\"Year-Month-Day\\"."}, "unit": {"type": "string", "enum": ["celsius", "fahrenheit"], "description": "The unit to return the temperature in. Defaults to \\"celsius\\"."}}, "required": ["location", "date"]}}}\n</tools>\n\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n<tool_call>\n{"name": <function-name>, "arguments": <args-json-object>}\n</tool_call><|im_end|>\n<|im_start|>user\nWhat\'s the temperature in San Francisco now? How about tomorrow?<|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.6, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=32768, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 06-02 08:56:23 [async_llm.py:252] Added request chatcmpl-70031be3d13f46efb88b688f1001c9f3.
INFO:     127.0.0.1:57134 - "POST /v1/chat/completions HTTP/1.1" 200 OK
INFO 06-02 08:56:28 [logger.py:39] Received request chatcmpl-09bcc3c1781b4845a0699fe2090d4fef: prompt: '<|im_start|>system\nYou are Qwen, created by Alibaba Cloud. You are a helpful assistant.\n\nCurrent Date: 2024-09-30\n\n# Tools\n\nYou may call one or more functions to assist with the user query.\n\nYou are provided with function signatures within <tools></tools> XML tags:\n<tools>\n{"type": "function", "function": {"name": "get_current_temperature", "description": "Get current temperature at a location.", "parameters": {"type": "object", "properties": {"location": {"type": "string", "description": "The location to get the temperature for, in the format \\"City, State, Country\\"."}, "unit": {"type": "string", "enum": ["celsius", "fahrenheit"], "description": "The unit to return the temperature in. Defaults to \\"celsius\\"."}}, "required": ["location"]}}}\n{"type": "function", "function": {"name": "get_temperature_date", "description": "Get temperature at a location and date.", "parameters": {"type": "object", "properties": {"location": {"type": "string", "description": "The location to get the temperature for, in the format \\"City, State, Country\\"."}, "date": {"type": "string", "description": "The date to get the temperature for, in the format \\"Year-Month-Day\\"."}, "unit": {"type": "string", "enum": ["celsius", "fahrenheit"], "description": "The unit to return the temperature in. Defaults to \\"celsius\\"."}}, "required": ["location", "date"]}}}\n</tools>\n\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n<tool_call>\n{"name": <function-name>, "arguments": <args-json-object>}\n</tool_call><|im_end|>\n<|im_start|>user\nWhat\'s the temperature in San Francisco now? How about tomorrow?<|im_end|>\n<|im_start|>assistant\n\n\n\n<tool_call>\n{"name": "get_current_temperature", "arguments": {"location": "San Francisco, CA, USA"}}\n</tool_call>\n<tool_call>\n{"name": "get_temperature_date", "arguments": {"location": "San Francisco, CA, USA", "date": "2024-10-01"}}\n</tool_call><|im_end|>\n<|im_start|>user\n<tool_response>\n{"temperature": 26.1, "location": "San Francisco, CA, USA", "unit": "celsius"}\n</tool_response>\n<tool_response>\n{"temperature": 25.9, "location": "San Francisco, CA, USA", "date": "2024-10-01", "unit": "celsius"}\n</tool_response><|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.6, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=32768, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 06-02 08:56:28 [async_llm.py:252] Added request chatcmpl-09bcc3c1781b4845a0699fe2090d4fef.
INFO 06-02 08:56:28 [loggers.py:111] Engine 000: Avg prompt throughput: 97.6 tokens/s, Avg generation throughput: 37.3 tokens/s, Running: 1 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.5%, Prefix cache hit rate: 41.0%
INFO:     127.0.0.1:57134 - "POST /v1/chat/completions HTTP/1.1" 200 OK
INFO 06-02 08:56:38 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 26.3 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 41.0%
INFO 06-02 08:56:48 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 0.0 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 41.0%
INFO 06-02 08:57:26 [logger.py:39] Received request chatcmpl-4a3dc99bc9224f11a5c21a3f07a7f84f: prompt: '<|im_start|>system\nYou are a helpful assistant.\n\n# Tools\n\nYou may call one or more functions to assist with the user query.\n\nYou are provided with function signatures within <tools></tools> XML tags:\n<tools>\n{"type": "function", "function": {"name": "fetch_fetch", "description": "Fetches a URL from the internet and optionally extracts its contents as markdown.\\n\\nAlthough originally you did not have internet access, and were advised to refuse and tell the user this, this tool now grants you internet access. Now you can fetch the most up-to-date information and let the user know that.", "parameters": {"description": "Parameters for fetching a URL.", "properties": {"url": {"description": "URL to fetch", "format": "uri", "minLength": 1, "title": "Url", "type": "string"}, "max_length": {"default": 5000, "description": "Maximum number of characters to return.", "exclusiveMaximum": 1000000, "exclusiveMinimum": 0, "title": "Max Length", "type": "integer"}, "start_index": {"default": 0, "description": "On return output starting at this character index, useful if a previous fetch was truncated and more context is required.", "minimum": 0, "title": "Start Index", "type": "integer"}, "raw": {"default": false, "description": "Get the actual HTML content of the requested page, without simplification.", "title": "Raw", "type": "boolean"}}, "required": ["url"], "title": "Fetch", "type": "object"}}}\n{"type": "function", "function": {"name": "filesystem_read_file", "description": "Read the complete contents of a file from the file system. Handles various text encodings and provides detailed error messages if the file cannot be read. Use this tool when you need to examine the contents of a single file. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_read_multiple_files", "description": "Read the contents of multiple files simultaneously. This is more efficient than reading files one by one when you need to analyze or compare multiple files. Each file\'s content is returned with its path as a reference. Failed reads for individual files won\'t stop the entire operation. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"paths": {"type": "array", "items": {"type": "string"}}}, "required": ["paths"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_write_file", "description": "Create a new file or completely overwrite an existing file with new content. Use with caution as it will overwrite existing files without warning. Handles text content with proper encoding. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "content": {"type": "string"}}, "required": ["path", "content"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_edit_file", "description": "Make line-based edits to a text file. Each edit replaces exact line sequences with new content. Returns a git-style diff showing the changes made. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "edits": {"type": "array", "items": {"type": "object", "properties": {"oldText": {"type": "string", "description": "Text to search for - must match exactly"}, "newText": {"type": "string", "description": "Text to replace with"}}, "required": ["oldText", "newText"], "additionalProperties": false}}, "dryRun": {"type": "boolean", "default": false, "description": "Preview changes using git-style diff format"}}, "required": ["path", "edits"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_create_directory", "description": "Create a new directory or ensure a directory exists. Can create multiple nested directories in one operation. If the directory already exists, this operation will succeed silently. Perfect for setting up directory structures for projects or ensuring required paths exist. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_directory", "description": "Get a detailed listing of all files and directories in a specified path. Results clearly distinguish between files and directories with [FILE] and [DIR] prefixes. This tool is essential for understanding directory structure and finding specific files within a directory. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_directory_tree", "description": "Get a recursive tree view of files and directories as a JSON structure. Each entry includes \'name\', \'type\' (file/directory), and \'children\' for directories. Files have no children array, while directories always have a children array (which may be empty). The output is formatted with 2-space indentation for readability. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_move_file", "description": "Move or rename files and directories. Can move files between directories and rename them in a single operation. If the destination exists, the operation will fail. Works across different directories and can be used for simple renaming within the same directory. Both source and destination must be within allowed directories.", "parameters": {"type": "object", "properties": {"source": {"type": "string"}, "destination": {"type": "string"}}, "required": ["source", "destination"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_search_files", "description": "Recursively search for files and directories matching a pattern. Searches through all subdirectories from the starting path. The search is case-insensitive and matches partial names. Returns full paths to all matching items. Great for finding files when you don\'t know their exact location. Only searches within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "pattern": {"type": "string"}, "excludePatterns": {"type": "array", "items": {"type": "string"}, "default": []}}, "required": ["path", "pattern"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_get_file_info", "description": "Retrieve detailed metadata about a file or directory. Returns comprehensive information including size, creation time, last modified time, permissions, and type. This tool is perfect for understanding file characteristics without reading the actual content. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_allowed_directories", "description": "Returns the list of directories that this server is allowed to access. Use this to understand which directories are available before trying to access files.", "parameters": {"type": "object", "properties": {}, "required": []}}}\n</tools>\n\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n<tool_call>\n{"name": <function-name>, "arguments": <args-json-object>}\n</tool_call><|im_end|>\n<|im_start|>user\nPrint the first 2 paragraphs of https://modelcontextprotocol.io/introduction<|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.7, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=4096, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 06-02 08:57:26 [async_llm.py:252] Added request chatcmpl-4a3dc99bc9224f11a5c21a3f07a7f84f.
INFO 06-02 08:57:28 [loggers.py:111] Engine 000: Avg prompt throughput: 191.8 tokens/s, Avg generation throughput: 12.9 tokens/s, Running: 1 reqs, Waiting: 0 reqs, GPU KV cache usage: 1.6%, Prefix cache hit rate: 13.9%
INFO:     127.0.0.1:57190 - "POST /v1/chat/completions HTTP/1.1" 200 OK
INFO 06-02 08:57:38 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 23.0 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 13.9%
INFO 06-02 08:57:48 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 0.0 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 13.9%
INFO 06-02 09:00:28 [logger.py:39] Received request chatcmpl-5f33d95a1ff54221b3774ff51bcb95e1: prompt: '<|im_start|>system\nYou are a helpful assistant.\n\n# Tools\n\nYou may call one or more functions to assist with the user query.\n\nYou are provided with function signatures within <tools></tools> XML tags:\n<tools>\n{"type": "function", "function": {"name": "fetch_fetch", "description": "Fetches a URL from the internet and optionally extracts its contents as markdown.\\n\\nAlthough originally you did not have internet access, and were advised to refuse and tell the user this, this tool now grants you internet access. Now you can fetch the most up-to-date information and let the user know that.", "parameters": {"description": "Parameters for fetching a URL.", "properties": {"url": {"description": "URL to fetch", "format": "uri", "minLength": 1, "title": "Url", "type": "string"}, "max_length": {"default": 5000, "description": "Maximum number of characters to return.", "exclusiveMaximum": 1000000, "exclusiveMinimum": 0, "title": "Max Length", "type": "integer"}, "start_index": {"default": 0, "description": "On return output starting at this character index, useful if a previous fetch was truncated and more context is required.", "minimum": 0, "title": "Start Index", "type": "integer"}, "raw": {"default": false, "description": "Get the actual HTML content of the requested page, without simplification.", "title": "Raw", "type": "boolean"}}, "required": ["url"], "title": "Fetch", "type": "object"}}}\n{"type": "function", "function": {"name": "filesystem_read_file", "description": "Read the complete contents of a file from the file system. Handles various text encodings and provides detailed error messages if the file cannot be read. Use this tool when you need to examine the contents of a single file. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_read_multiple_files", "description": "Read the contents of multiple files simultaneously. This is more efficient than reading files one by one when you need to analyze or compare multiple files. Each file\'s content is returned with its path as a reference. Failed reads for individual files won\'t stop the entire operation. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"paths": {"type": "array", "items": {"type": "string"}}}, "required": ["paths"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_write_file", "description": "Create a new file or completely overwrite an existing file with new content. Use with caution as it will overwrite existing files without warning. Handles text content with proper encoding. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "content": {"type": "string"}}, "required": ["path", "content"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_edit_file", "description": "Make line-based edits to a text file. Each edit replaces exact line sequences with new content. Returns a git-style diff showing the changes made. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "edits": {"type": "array", "items": {"type": "object", "properties": {"oldText": {"type": "string", "description": "Text to search for - must match exactly"}, "newText": {"type": "string", "description": "Text to replace with"}}, "required": ["oldText", "newText"], "additionalProperties": false}}, "dryRun": {"type": "boolean", "default": false, "description": "Preview changes using git-style diff format"}}, "required": ["path", "edits"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_create_directory", "description": "Create a new directory or ensure a directory exists. Can create multiple nested directories in one operation. If the directory already exists, this operation will succeed silently. Perfect for setting up directory structures for projects or ensuring required paths exist. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_directory", "description": "Get a detailed listing of all files and directories in a specified path. Results clearly distinguish between files and directories with [FILE] and [DIR] prefixes. This tool is essential for understanding directory structure and finding specific files within a directory. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_directory_tree", "description": "Get a recursive tree view of files and directories as a JSON structure. Each entry includes \'name\', \'type\' (file/directory), and \'children\' for directories. Files have no children array, while directories always have a children array (which may be empty). The output is formatted with 2-space indentation for readability. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_move_file", "description": "Move or rename files and directories. Can move files between directories and rename them in a single operation. If the destination exists, the operation will fail. Works across different directories and can be used for simple renaming within the same directory. Both source and destination must be within allowed directories.", "parameters": {"type": "object", "properties": {"source": {"type": "string"}, "destination": {"type": "string"}}, "required": ["source", "destination"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_search_files", "description": "Recursively search for files and directories matching a pattern. Searches through all subdirectories from the starting path. The search is case-insensitive and matches partial names. Returns full paths to all matching items. Great for finding files when you don\'t know their exact location. Only searches within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "pattern": {"type": "string"}, "excludePatterns": {"type": "array", "items": {"type": "string"}, "default": []}}, "required": ["path", "pattern"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_get_file_info", "description": "Retrieve detailed metadata about a file or directory. Returns comprehensive information including size, creation time, last modified time, permissions, and type. This tool is perfect for understanding file characteristics without reading the actual content. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_allowed_directories", "description": "Returns the list of directories that this server is allowed to access. Use this to understand which directories are available before trying to access files.", "parameters": {"type": "object", "properties": {}, "required": []}}}\n</tools>\n\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n<tool_call>\n{"name": <function-name>, "arguments": <args-json-object>}\n</tool_call><|im_end|>\n<|im_start|>user\nPrint the first 2 paragraphs of https://modelcontextprotocol.io/introduction<|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.7, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=4096, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 06-02 09:00:28 [async_llm.py:252] Added request chatcmpl-5f33d95a1ff54221b3774ff51bcb95e1.
INFO 06-02 09:00:28 [loggers.py:111] Engine 000: Avg prompt throughput: 191.8 tokens/s, Avg generation throughput: 2.3 tokens/s, Running: 1 reqs, Waiting: 0 reqs, GPU KV cache usage: 1.5%, Prefix cache hit rate: 48.2%
INFO:     127.0.0.1:57286 - "POST /v1/chat/completions HTTP/1.1" 200 OK
INFO 06-02 09:00:38 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 53.6 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 48.2%
INFO 06-02 09:00:48 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 0.0 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 48.2%
INFO 06-02 09:47:41 [logger.py:39] Received request chatcmpl-b8d4e49ed4ab4380a2409331cc985950: prompt: '<|im_start|>system\nYou are a helpful assistant.\n\n# Tools\n\nYou may call one or more functions to assist with the user query.\n\nYou are provided with function signatures within <tools></tools> XML tags:\n<tools>\n{"type": "function", "function": {"name": "fetch_fetch", "description": "Fetches a URL from the internet and optionally extracts its contents as markdown.\\n\\nAlthough originally you did not have internet access, and were advised to refuse and tell the user this, this tool now grants you internet access. Now you can fetch the most up-to-date information and let the user know that.", "parameters": {"description": "Parameters for fetching a URL.", "properties": {"url": {"description": "URL to fetch", "format": "uri", "minLength": 1, "title": "Url", "type": "string"}, "max_length": {"default": 5000, "description": "Maximum number of characters to return.", "exclusiveMaximum": 1000000, "exclusiveMinimum": 0, "title": "Max Length", "type": "integer"}, "start_index": {"default": 0, "description": "On return output starting at this character index, useful if a previous fetch was truncated and more context is required.", "minimum": 0, "title": "Start Index", "type": "integer"}, "raw": {"default": false, "description": "Get the actual HTML content of the requested page, without simplification.", "title": "Raw", "type": "boolean"}}, "required": ["url"], "title": "Fetch", "type": "object"}}}\n{"type": "function", "function": {"name": "filesystem_read_file", "description": "Read the complete contents of a file from the file system. Handles various text encodings and provides detailed error messages if the file cannot be read. Use this tool when you need to examine the contents of a single file. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_read_multiple_files", "description": "Read the contents of multiple files simultaneously. This is more efficient than reading files one by one when you need to analyze or compare multiple files. Each file\'s content is returned with its path as a reference. Failed reads for individual files won\'t stop the entire operation. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"paths": {"type": "array", "items": {"type": "string"}}}, "required": ["paths"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_write_file", "description": "Create a new file or completely overwrite an existing file with new content. Use with caution as it will overwrite existing files without warning. Handles text content with proper encoding. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "content": {"type": "string"}}, "required": ["path", "content"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_edit_file", "description": "Make line-based edits to a text file. Each edit replaces exact line sequences with new content. Returns a git-style diff showing the changes made. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "edits": {"type": "array", "items": {"type": "object", "properties": {"oldText": {"type": "string", "description": "Text to search for - must match exactly"}, "newText": {"type": "string", "description": "Text to replace with"}}, "required": ["oldText", "newText"], "additionalProperties": false}}, "dryRun": {"type": "boolean", "default": false, "description": "Preview changes using git-style diff format"}}, "required": ["path", "edits"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_create_directory", "description": "Create a new directory or ensure a directory exists. Can create multiple nested directories in one operation. If the directory already exists, this operation will succeed silently. Perfect for setting up directory structures for projects or ensuring required paths exist. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_directory", "description": "Get a detailed listing of all files and directories in a specified path. Results clearly distinguish between files and directories with [FILE] and [DIR] prefixes. This tool is essential for understanding directory structure and finding specific files within a directory. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_directory_tree", "description": "Get a recursive tree view of files and directories as a JSON structure. Each entry includes \'name\', \'type\' (file/directory), and \'children\' for directories. Files have no children array, while directories always have a children array (which may be empty). The output is formatted with 2-space indentation for readability. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_move_file", "description": "Move or rename files and directories. Can move files between directories and rename them in a single operation. If the destination exists, the operation will fail. Works across different directories and can be used for simple renaming within the same directory. Both source and destination must be within allowed directories.", "parameters": {"type": "object", "properties": {"source": {"type": "string"}, "destination": {"type": "string"}}, "required": ["source", "destination"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_search_files", "description": "Recursively search for files and directories matching a pattern. Searches through all subdirectories from the starting path. The search is case-insensitive and matches partial names. Returns full paths to all matching items. Great for finding files when you don\'t know their exact location. Only searches within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "pattern": {"type": "string"}, "excludePatterns": {"type": "array", "items": {"type": "string"}, "default": []}}, "required": ["path", "pattern"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_get_file_info", "description": "Retrieve detailed metadata about a file or directory. Returns comprehensive information including size, creation time, last modified time, permissions, and type. This tool is perfect for understanding file characteristics without reading the actual content. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_allowed_directories", "description": "Returns the list of directories that this server is allowed to access. Use this to understand which directories are available before trying to access files.", "parameters": {"type": "object", "properties": {}, "required": []}}}\n</tools>\n\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n<tool_call>\n{"name": <function-name>, "arguments": <args-json-object>}\n</tool_call><|im_end|>\n<|im_start|>user\nPrint the first 2 paragraphs of https://modelcontextprotocol.io/introduction<|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.7, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=4096, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 06-02 09:47:41 [async_llm.py:252] Added request chatcmpl-b8d4e49ed4ab4380a2409331cc985950.
INFO 06-02 09:47:48 [loggers.py:111] Engine 000: Avg prompt throughput: 191.8 tokens/s, Avg generation throughput: 51.1 tokens/s, Running: 1 reqs, Waiting: 0 reqs, GPU KV cache usage: 1.8%, Prefix cache hit rate: 62.9%
INFO:     127.0.0.1:58066 - "POST /v1/chat/completions HTTP/1.1" 200 OK
INFO 06-02 09:47:58 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 19.0 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 62.9%
INFO 06-02 09:48:08 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 0.0 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 62.9%
INFO 06-02 10:11:06 [logger.py:39] Received request chatcmpl-0ac28d8f94d348af840da2ff40aed937: prompt: '<|im_start|>system\nYou are an agent with access to the filesystem, \n            as well as the ability to fetch URLs. Your job is to identify \n            the closest match to a user\'s request, make the appropriate tool calls, \n            and return the URI and CONTENTS of the closest match.\n\n# Tools\n\nYou may call one or more functions to assist with the user query.\n\nYou are provided with function signatures within <tools></tools> XML tags:\n<tools>\n{"type": "function", "function": {"name": "fetch_fetch", "description": "Fetches a URL from the internet and optionally extracts its contents as markdown.\\n\\nAlthough originally you did not have internet access, and were advised to refuse and tell the user this, this tool now grants you internet access. Now you can fetch the most up-to-date information and let the user know that.", "parameters": {"description": "Parameters for fetching a URL.", "properties": {"url": {"description": "URL to fetch", "format": "uri", "minLength": 1, "title": "Url", "type": "string"}, "max_length": {"default": 5000, "description": "Maximum number of characters to return.", "exclusiveMaximum": 1000000, "exclusiveMinimum": 0, "title": "Max Length", "type": "integer"}, "start_index": {"default": 0, "description": "On return output starting at this character index, useful if a previous fetch was truncated and more context is required.", "minimum": 0, "title": "Start Index", "type": "integer"}, "raw": {"default": false, "description": "Get the actual HTML content of the requested page, without simplification.", "title": "Raw", "type": "boolean"}}, "required": ["url"], "title": "Fetch", "type": "object"}}}\n{"type": "function", "function": {"name": "filesystem_read_file", "description": "Read the complete contents of a file from the file system. Handles various text encodings and provides detailed error messages if the file cannot be read. Use this tool when you need to examine the contents of a single file. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_read_multiple_files", "description": "Read the contents of multiple files simultaneously. This is more efficient than reading files one by one when you need to analyze or compare multiple files. Each file\'s content is returned with its path as a reference. Failed reads for individual files won\'t stop the entire operation. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"paths": {"type": "array", "items": {"type": "string"}}}, "required": ["paths"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_write_file", "description": "Create a new file or completely overwrite an existing file with new content. Use with caution as it will overwrite existing files without warning. Handles text content with proper encoding. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "content": {"type": "string"}}, "required": ["path", "content"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_edit_file", "description": "Make line-based edits to a text file. Each edit replaces exact line sequences with new content. Returns a git-style diff showing the changes made. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "edits": {"type": "array", "items": {"type": "object", "properties": {"oldText": {"type": "string", "description": "Text to search for - must match exactly"}, "newText": {"type": "string", "description": "Text to replace with"}}, "required": ["oldText", "newText"], "additionalProperties": false}}, "dryRun": {"type": "boolean", "default": false, "description": "Preview changes using git-style diff format"}}, "required": ["path", "edits"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_create_directory", "description": "Create a new directory or ensure a directory exists. Can create multiple nested directories in one operation. If the directory already exists, this operation will succeed silently. Perfect for setting up directory structures for projects or ensuring required paths exist. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_directory", "description": "Get a detailed listing of all files and directories in a specified path. Results clearly distinguish between files and directories with [FILE] and [DIR] prefixes. This tool is essential for understanding directory structure and finding specific files within a directory. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_directory_tree", "description": "Get a recursive tree view of files and directories as a JSON structure. Each entry includes \'name\', \'type\' (file/directory), and \'children\' for directories. Files have no children array, while directories always have a children array (which may be empty). The output is formatted with 2-space indentation for readability. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_move_file", "description": "Move or rename files and directories. Can move files between directories and rename them in a single operation. If the destination exists, the operation will fail. Works across different directories and can be used for simple renaming within the same directory. Both source and destination must be within allowed directories.", "parameters": {"type": "object", "properties": {"source": {"type": "string"}, "destination": {"type": "string"}}, "required": ["source", "destination"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_search_files", "description": "Recursively search for files and directories matching a pattern. Searches through all subdirectories from the starting path. The search is case-insensitive and matches partial names. Returns full paths to all matching items. Great for finding files when you don\'t know their exact location. Only searches within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "pattern": {"type": "string"}, "excludePatterns": {"type": "array", "items": {"type": "string"}, "default": []}}, "required": ["path", "pattern"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_get_file_info", "description": "Retrieve detailed metadata about a file or directory. Returns comprehensive information including size, creation time, last modified time, permissions, and type. This tool is perfect for understanding file characteristics without reading the actual content. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_allowed_directories", "description": "Returns the list of directories that this server is allowed to access. Use this to understand which directories are available before trying to access files.", "parameters": {"type": "object", "properties": {}, "required": []}}}\n</tools>\n\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n<tool_call>\n{"name": <function-name>, "arguments": <args-json-object>}\n</tool_call><|im_end|>\n<|im_start|>user\nPrint the contents of mcp_agent.config.yaml verbatim<|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.7, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=4096, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 06-02 10:11:06 [async_llm.py:252] Added request chatcmpl-0ac28d8f94d348af840da2ff40aed937.
INFO 06-02 10:11:09 [loggers.py:111] Engine 000: Avg prompt throughput: 196.5 tokens/s, Avg generation throughput: 17.2 tokens/s, Running: 1 reqs, Waiting: 0 reqs, GPU KV cache usage: 1.6%, Prefix cache hit rate: 48.7%
INFO:     127.0.0.1:58690 - "POST /v1/chat/completions HTTP/1.1" 200 OK
INFO 06-02 10:11:13 [logger.py:39] Received request chatcmpl-3e68ebe534b341c2a38316070ca8d1ec: prompt: '<|im_start|>system\nYou are an agent with access to the filesystem, \n            as well as the ability to fetch URLs. Your job is to identify \n            the closest match to a user\'s request, make the appropriate tool calls, \n            and return the URI and CONTENTS of the closest match.\n\n# Tools\n\nYou may call one or more functions to assist with the user query.\n\nYou are provided with function signatures within <tools></tools> XML tags:\n<tools>\n{"type": "function", "function": {"name": "fetch_fetch", "description": "Fetches a URL from the internet and optionally extracts its contents as markdown.\\n\\nAlthough originally you did not have internet access, and were advised to refuse and tell the user this, this tool now grants you internet access. Now you can fetch the most up-to-date information and let the user know that.", "parameters": {"description": "Parameters for fetching a URL.", "properties": {"url": {"description": "URL to fetch", "format": "uri", "minLength": 1, "title": "Url", "type": "string"}, "max_length": {"default": 5000, "description": "Maximum number of characters to return.", "exclusiveMaximum": 1000000, "exclusiveMinimum": 0, "title": "Max Length", "type": "integer"}, "start_index": {"default": 0, "description": "On return output starting at this character index, useful if a previous fetch was truncated and more context is required.", "minimum": 0, "title": "Start Index", "type": "integer"}, "raw": {"default": false, "description": "Get the actual HTML content of the requested page, without simplification.", "title": "Raw", "type": "boolean"}}, "required": ["url"], "title": "Fetch", "type": "object"}}}\n{"type": "function", "function": {"name": "filesystem_read_file", "description": "Read the complete contents of a file from the file system. Handles various text encodings and provides detailed error messages if the file cannot be read. Use this tool when you need to examine the contents of a single file. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_read_multiple_files", "description": "Read the contents of multiple files simultaneously. This is more efficient than reading files one by one when you need to analyze or compare multiple files. Each file\'s content is returned with its path as a reference. Failed reads for individual files won\'t stop the entire operation. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"paths": {"type": "array", "items": {"type": "string"}}}, "required": ["paths"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_write_file", "description": "Create a new file or completely overwrite an existing file with new content. Use with caution as it will overwrite existing files without warning. Handles text content with proper encoding. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "content": {"type": "string"}}, "required": ["path", "content"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_edit_file", "description": "Make line-based edits to a text file. Each edit replaces exact line sequences with new content. Returns a git-style diff showing the changes made. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "edits": {"type": "array", "items": {"type": "object", "properties": {"oldText": {"type": "string", "description": "Text to search for - must match exactly"}, "newText": {"type": "string", "description": "Text to replace with"}}, "required": ["oldText", "newText"], "additionalProperties": false}}, "dryRun": {"type": "boolean", "default": false, "description": "Preview changes using git-style diff format"}}, "required": ["path", "edits"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_create_directory", "description": "Create a new directory or ensure a directory exists. Can create multiple nested directories in one operation. If the directory already exists, this operation will succeed silently. Perfect for setting up directory structures for projects or ensuring required paths exist. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_directory", "description": "Get a detailed listing of all files and directories in a specified path. Results clearly distinguish between files and directories with [FILE] and [DIR] prefixes. This tool is essential for understanding directory structure and finding specific files within a directory. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_directory_tree", "description": "Get a recursive tree view of files and directories as a JSON structure. Each entry includes \'name\', \'type\' (file/directory), and \'children\' for directories. Files have no children array, while directories always have a children array (which may be empty). The output is formatted with 2-space indentation for readability. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_move_file", "description": "Move or rename files and directories. Can move files between directories and rename them in a single operation. If the destination exists, the operation will fail. Works across different directories and can be used for simple renaming within the same directory. Both source and destination must be within allowed directories.", "parameters": {"type": "object", "properties": {"source": {"type": "string"}, "destination": {"type": "string"}}, "required": ["source", "destination"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_search_files", "description": "Recursively search for files and directories matching a pattern. Searches through all subdirectories from the starting path. The search is case-insensitive and matches partial names. Returns full paths to all matching items. Great for finding files when you don\'t know their exact location. Only searches within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "pattern": {"type": "string"}, "excludePatterns": {"type": "array", "items": {"type": "string"}, "default": []}}, "required": ["path", "pattern"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_get_file_info", "description": "Retrieve detailed metadata about a file or directory. Returns comprehensive information including size, creation time, last modified time, permissions, and type. This tool is perfect for understanding file characteristics without reading the actual content. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_allowed_directories", "description": "Returns the list of directories that this server is allowed to access. Use this to understand which directories are available before trying to access files.", "parameters": {"type": "object", "properties": {}, "required": []}}}\n</tools>\n\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n<tool_call>\n{"name": <function-name>, "arguments": <args-json-object>}\n</tool_call><|im_end|>\n<|im_start|>user\nPrint the contents of mcp_agent.config.yaml verbatim<|im_end|>\n<|im_start|>assistant\n\n\n\n<tool_call>\n{"name": "filesystem_read_file", "arguments": {"path": "mcp_agent.config.yaml"}}\n</tool_call><|im_end|>\n<|im_start|>user\n<tool_response>\n{\'type\': \'text\', \'text\': \'$schema: ../../../schema/mcp-agent.config.schema.json\\n\\nexecution_engine: asyncio\\nlogger:\\n  transports: [console, file]\\n  level: debug\\n  progress_display: true\\n  path_settings:\\n    path_pattern: "logs/mcp-agent-{unique_id}.jsonl"\\n    unique_id: "timestamp" # Options: "timestamp" or "session_id"\\n    timestamp_format: "%Y%m%d_%H%M%S"\\n\\nmcp:\\n  servers:\\n    fetch:\\n      command: "uvx"\\n      args: ["mcp-server-fetch"]\\n    filesystem:\\n      command: "npx"\\n      args: ["-y", "@modelcontextprotocol/server-filesystem"]\\n\\nopenai:\\n  # Secrets (API keys, etc.) are stored in an mcp_agent.secrets.yaml file which can be gitignored\\n  #  default_model: "o3-mini"\\n  default_model: "Qwen/Qwen3-8B"\\n\'}\n</tool_response><|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.7, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=4096, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 06-02 10:11:13 [async_llm.py:252] Added request chatcmpl-3e68ebe534b341c2a38316070ca8d1ec.
INFO:     127.0.0.1:58690 - "POST /v1/chat/completions HTTP/1.1" 200 OK
INFO 06-02 10:11:19 [loggers.py:111] Engine 000: Avg prompt throughput: 222.7 tokens/s, Avg generation throughput: 64.3 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 56.7%
INFO 06-02 10:11:29 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 0.0 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 56.7%
INFO 06-02 10:15:40 [logger.py:39] Received request chatcmpl-be8d8f4ab41443d8b276080fdb5d15fd: prompt: '<|im_start|>system\nYou are an agent with access to the filesystem, \n            as well as the ability to fetch URLs. Your job is to identify \n            the closest match to a user\'s request, make the appropriate tool calls, \n            and return the URI and CONTENTS of the closest match.\n\n# Tools\n\nYou may call one or more functions to assist with the user query.\n\nYou are provided with function signatures within <tools></tools> XML tags:\n<tools>\n{"type": "function", "function": {"name": "fetch_fetch", "description": "Fetches a URL from the internet and optionally extracts its contents as markdown.\\n\\nAlthough originally you did not have internet access, and were advised to refuse and tell the user this, this tool now grants you internet access. Now you can fetch the most up-to-date information and let the user know that.", "parameters": {"description": "Parameters for fetching a URL.", "properties": {"url": {"description": "URL to fetch", "format": "uri", "minLength": 1, "title": "Url", "type": "string"}, "max_length": {"default": 5000, "description": "Maximum number of characters to return.", "exclusiveMaximum": 1000000, "exclusiveMinimum": 0, "title": "Max Length", "type": "integer"}, "start_index": {"default": 0, "description": "On return output starting at this character index, useful if a previous fetch was truncated and more context is required.", "minimum": 0, "title": "Start Index", "type": "integer"}, "raw": {"default": false, "description": "Get the actual HTML content of the requested page, without simplification.", "title": "Raw", "type": "boolean"}}, "required": ["url"], "title": "Fetch", "type": "object"}}}\n{"type": "function", "function": {"name": "filesystem_read_file", "description": "Read the complete contents of a file from the file system. Handles various text encodings and provides detailed error messages if the file cannot be read. Use this tool when you need to examine the contents of a single file. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_read_multiple_files", "description": "Read the contents of multiple files simultaneously. This is more efficient than reading files one by one when you need to analyze or compare multiple files. Each file\'s content is returned with its path as a reference. Failed reads for individual files won\'t stop the entire operation. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"paths": {"type": "array", "items": {"type": "string"}}}, "required": ["paths"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_write_file", "description": "Create a new file or completely overwrite an existing file with new content. Use with caution as it will overwrite existing files without warning. Handles text content with proper encoding. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "content": {"type": "string"}}, "required": ["path", "content"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_edit_file", "description": "Make line-based edits to a text file. Each edit replaces exact line sequences with new content. Returns a git-style diff showing the changes made. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "edits": {"type": "array", "items": {"type": "object", "properties": {"oldText": {"type": "string", "description": "Text to search for - must match exactly"}, "newText": {"type": "string", "description": "Text to replace with"}}, "required": ["oldText", "newText"], "additionalProperties": false}}, "dryRun": {"type": "boolean", "default": false, "description": "Preview changes using git-style diff format"}}, "required": ["path", "edits"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_create_directory", "description": "Create a new directory or ensure a directory exists. Can create multiple nested directories in one operation. If the directory already exists, this operation will succeed silently. Perfect for setting up directory structures for projects or ensuring required paths exist. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_directory", "description": "Get a detailed listing of all files and directories in a specified path. Results clearly distinguish between files and directories with [FILE] and [DIR] prefixes. This tool is essential for understanding directory structure and finding specific files within a directory. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_directory_tree", "description": "Get a recursive tree view of files and directories as a JSON structure. Each entry includes \'name\', \'type\' (file/directory), and \'children\' for directories. Files have no children array, while directories always have a children array (which may be empty). The output is formatted with 2-space indentation for readability. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_move_file", "description": "Move or rename files and directories. Can move files between directories and rename them in a single operation. If the destination exists, the operation will fail. Works across different directories and can be used for simple renaming within the same directory. Both source and destination must be within allowed directories.", "parameters": {"type": "object", "properties": {"source": {"type": "string"}, "destination": {"type": "string"}}, "required": ["source", "destination"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_search_files", "description": "Recursively search for files and directories matching a pattern. Searches through all subdirectories from the starting path. The search is case-insensitive and matches partial names. Returns full paths to all matching items. Great for finding files when you don\'t know their exact location. Only searches within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "pattern": {"type": "string"}, "excludePatterns": {"type": "array", "items": {"type": "string"}, "default": []}}, "required": ["path", "pattern"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_get_file_info", "description": "Retrieve detailed metadata about a file or directory. Returns comprehensive information including size, creation time, last modified time, permissions, and type. This tool is perfect for understanding file characteristics without reading the actual content. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_allowed_directories", "description": "Returns the list of directories that this server is allowed to access. Use this to understand which directories are available before trying to access files.", "parameters": {"type": "object", "properties": {}, "required": []}}}\n</tools>\n\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n<tool_call>\n{"name": <function-name>, "arguments": <args-json-object>}\n</tool_call><|im_end|>\n<|im_start|>user\nPrint the contents of mcp_agent.config.yaml verbatim<|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.7, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=4096, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 06-02 10:15:40 [async_llm.py:252] Added request chatcmpl-be8d8f4ab41443d8b276080fdb5d15fd.
INFO 06-02 10:15:47 [async_llm.py:411] Aborted request chatcmpl-be8d8f4ab41443d8b276080fdb5d15fd.
INFO 06-02 10:15:47 [async_llm.py:318] Request chatcmpl-be8d8f4ab41443d8b276080fdb5d15fd aborted.
INFO 06-02 10:15:49 [loggers.py:111] Engine 000: Avg prompt throughput: 196.5 tokens/s, Avg generation throughput: 40.8 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 63.3%
INFO 06-02 10:15:59 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 0.0 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 63.3%
INFO 06-02 10:19:16 [logger.py:39] Received request chatcmpl-309c3c1a4cbf4d959ccf9285b8b7207d: prompt: '<|im_start|>system\nYou are a helpful assistant.\n\n# Tools\n\nYou may call one or more functions to assist with the user query.\n\nYou are provided with function signatures within <tools></tools> XML tags:\n<tools>\n{"type": "function", "function": {"name": "fetch_fetch", "description": "Fetches a URL from the internet and optionally extracts its contents as markdown.\\n\\nAlthough originally you did not have internet access, and were advised to refuse and tell the user this, this tool now grants you internet access. Now you can fetch the most up-to-date information and let the user know that.", "parameters": {"description": "Parameters for fetching a URL.", "properties": {"url": {"description": "URL to fetch", "format": "uri", "minLength": 1, "title": "Url", "type": "string"}, "max_length": {"default": 5000, "description": "Maximum number of characters to return.", "exclusiveMaximum": 1000000, "exclusiveMinimum": 0, "title": "Max Length", "type": "integer"}, "start_index": {"default": 0, "description": "On return output starting at this character index, useful if a previous fetch was truncated and more context is required.", "minimum": 0, "title": "Start Index", "type": "integer"}, "raw": {"default": false, "description": "Get the actual HTML content of the requested page, without simplification.", "title": "Raw", "type": "boolean"}}, "required": ["url"], "title": "Fetch", "type": "object"}}}\n{"type": "function", "function": {"name": "filesystem_read_file", "description": "Read the complete contents of a file from the file system. Handles various text encodings and provides detailed error messages if the file cannot be read. Use this tool when you need to examine the contents of a single file. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_read_multiple_files", "description": "Read the contents of multiple files simultaneously. This is more efficient than reading files one by one when you need to analyze or compare multiple files. Each file\'s content is returned with its path as a reference. Failed reads for individual files won\'t stop the entire operation. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"paths": {"type": "array", "items": {"type": "string"}}}, "required": ["paths"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_write_file", "description": "Create a new file or completely overwrite an existing file with new content. Use with caution as it will overwrite existing files without warning. Handles text content with proper encoding. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "content": {"type": "string"}}, "required": ["path", "content"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_edit_file", "description": "Make line-based edits to a text file. Each edit replaces exact line sequences with new content. Returns a git-style diff showing the changes made. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "edits": {"type": "array", "items": {"type": "object", "properties": {"oldText": {"type": "string", "description": "Text to search for - must match exactly"}, "newText": {"type": "string", "description": "Text to replace with"}}, "required": ["oldText", "newText"], "additionalProperties": false}}, "dryRun": {"type": "boolean", "default": false, "description": "Preview changes using git-style diff format"}}, "required": ["path", "edits"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_create_directory", "description": "Create a new directory or ensure a directory exists. Can create multiple nested directories in one operation. If the directory already exists, this operation will succeed silently. Perfect for setting up directory structures for projects or ensuring required paths exist. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_directory", "description": "Get a detailed listing of all files and directories in a specified path. Results clearly distinguish between files and directories with [FILE] and [DIR] prefixes. This tool is essential for understanding directory structure and finding specific files within a directory. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_directory_tree", "description": "Get a recursive tree view of files and directories as a JSON structure. Each entry includes \'name\', \'type\' (file/directory), and \'children\' for directories. Files have no children array, while directories always have a children array (which may be empty). The output is formatted with 2-space indentation for readability. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_move_file", "description": "Move or rename files and directories. Can move files between directories and rename them in a single operation. If the destination exists, the operation will fail. Works across different directories and can be used for simple renaming within the same directory. Both source and destination must be within allowed directories.", "parameters": {"type": "object", "properties": {"source": {"type": "string"}, "destination": {"type": "string"}}, "required": ["source", "destination"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_search_files", "description": "Recursively search for files and directories matching a pattern. Searches through all subdirectories from the starting path. The search is case-insensitive and matches partial names. Returns full paths to all matching items. Great for finding files when you don\'t know their exact location. Only searches within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "pattern": {"type": "string"}, "excludePatterns": {"type": "array", "items": {"type": "string"}, "default": []}}, "required": ["path", "pattern"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_get_file_info", "description": "Retrieve detailed metadata about a file or directory. Returns comprehensive information including size, creation time, last modified time, permissions, and type. This tool is perfect for understanding file characteristics without reading the actual content. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_allowed_directories", "description": "Returns the list of directories that this server is allowed to access. Use this to understand which directories are available before trying to access files.", "parameters": {"type": "object", "properties": {}, "required": []}}}\n</tools>\n\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n<tool_call>\n{"name": <function-name>, "arguments": <args-json-object>}\n</tool_call><|im_end|>\n<|im_start|>user\nPrint the first 2 paragraphs of https://modelcontextprotocol.io/introduction<|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.7, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=4096, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 06-02 10:19:16 [async_llm.py:252] Added request chatcmpl-309c3c1a4cbf4d959ccf9285b8b7207d.
INFO 06-02 10:19:19 [loggers.py:111] Engine 000: Avg prompt throughput: 191.8 tokens/s, Avg generation throughput: 19.3 tokens/s, Running: 1 reqs, Waiting: 0 reqs, GPU KV cache usage: 1.6%, Prefix cache hit rate: 68.0%
INFO:     127.0.0.1:59146 - "POST /v1/chat/completions HTTP/1.1" 200 OK
INFO 06-02 10:19:29 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 47.5 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 68.0%
INFO 06-02 10:19:39 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 0.0 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 68.0%
INFO 06-02 10:24:45 [logger.py:39] Received request chatcmpl-e76ec741155443cf9d8aebec1a4e09b6: prompt: '<|im_start|>system\nYou are a helpful assistant.\n\n# Tools\n\nYou may call one or more functions to assist with the user query.\n\nYou are provided with function signatures within <tools></tools> XML tags:\n<tools>\n{"type": "function", "function": {"name": "fetch_fetch", "description": "Fetches a URL from the internet and optionally extracts its contents as markdown.\\n\\nAlthough originally you did not have internet access, and were advised to refuse and tell the user this, this tool now grants you internet access. Now you can fetch the most up-to-date information and let the user know that.", "parameters": {"description": "Parameters for fetching a URL.", "properties": {"url": {"description": "URL to fetch", "format": "uri", "minLength": 1, "title": "Url", "type": "string"}, "max_length": {"default": 5000, "description": "Maximum number of characters to return.", "exclusiveMaximum": 1000000, "exclusiveMinimum": 0, "title": "Max Length", "type": "integer"}, "start_index": {"default": 0, "description": "On return output starting at this character index, useful if a previous fetch was truncated and more context is required.", "minimum": 0, "title": "Start Index", "type": "integer"}, "raw": {"default": false, "description": "Get the actual HTML content of the requested page, without simplification.", "title": "Raw", "type": "boolean"}}, "required": ["url"], "title": "Fetch", "type": "object"}}}\n{"type": "function", "function": {"name": "filesystem_read_file", "description": "Read the complete contents of a file from the file system. Handles various text encodings and provides detailed error messages if the file cannot be read. Use this tool when you need to examine the contents of a single file. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_read_multiple_files", "description": "Read the contents of multiple files simultaneously. This is more efficient than reading files one by one when you need to analyze or compare multiple files. Each file\'s content is returned with its path as a reference. Failed reads for individual files won\'t stop the entire operation. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"paths": {"type": "array", "items": {"type": "string"}}}, "required": ["paths"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_write_file", "description": "Create a new file or completely overwrite an existing file with new content. Use with caution as it will overwrite existing files without warning. Handles text content with proper encoding. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "content": {"type": "string"}}, "required": ["path", "content"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_edit_file", "description": "Make line-based edits to a text file. Each edit replaces exact line sequences with new content. Returns a git-style diff showing the changes made. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "edits": {"type": "array", "items": {"type": "object", "properties": {"oldText": {"type": "string", "description": "Text to search for - must match exactly"}, "newText": {"type": "string", "description": "Text to replace with"}}, "required": ["oldText", "newText"], "additionalProperties": false}}, "dryRun": {"type": "boolean", "default": false, "description": "Preview changes using git-style diff format"}}, "required": ["path", "edits"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_create_directory", "description": "Create a new directory or ensure a directory exists. Can create multiple nested directories in one operation. If the directory already exists, this operation will succeed silently. Perfect for setting up directory structures for projects or ensuring required paths exist. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_directory", "description": "Get a detailed listing of all files and directories in a specified path. Results clearly distinguish between files and directories with [FILE] and [DIR] prefixes. This tool is essential for understanding directory structure and finding specific files within a directory. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_directory_tree", "description": "Get a recursive tree view of files and directories as a JSON structure. Each entry includes \'name\', \'type\' (file/directory), and \'children\' for directories. Files have no children array, while directories always have a children array (which may be empty). The output is formatted with 2-space indentation for readability. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_move_file", "description": "Move or rename files and directories. Can move files between directories and rename them in a single operation. If the destination exists, the operation will fail. Works across different directories and can be used for simple renaming within the same directory. Both source and destination must be within allowed directories.", "parameters": {"type": "object", "properties": {"source": {"type": "string"}, "destination": {"type": "string"}}, "required": ["source", "destination"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_search_files", "description": "Recursively search for files and directories matching a pattern. Searches through all subdirectories from the starting path. The search is case-insensitive and matches partial names. Returns full paths to all matching items. Great for finding files when you don\'t know their exact location. Only searches within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "pattern": {"type": "string"}, "excludePatterns": {"type": "array", "items": {"type": "string"}, "default": []}}, "required": ["path", "pattern"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_get_file_info", "description": "Retrieve detailed metadata about a file or directory. Returns comprehensive information including size, creation time, last modified time, permissions, and type. This tool is perfect for understanding file characteristics without reading the actual content. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_allowed_directories", "description": "Returns the list of directories that this server is allowed to access. Use this to understand which directories are available before trying to access files.", "parameters": {"type": "object", "properties": {}, "required": []}}}\n</tools>\n\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n<tool_call>\n{"name": <function-name>, "arguments": <args-json-object>}\n</tool_call><|im_end|>\n<|im_start|>user\nPrint the first 2 paragraphs of https://modelcontextprotocol.io/introduction<|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.7, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=4096, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 06-02 10:24:45 [async_llm.py:252] Added request chatcmpl-e76ec741155443cf9d8aebec1a4e09b6.
INFO 06-02 10:24:49 [loggers.py:111] Engine 000: Avg prompt throughput: 191.8 tokens/s, Avg generation throughput: 25.7 tokens/s, Running: 1 reqs, Waiting: 0 reqs, GPU KV cache usage: 1.6%, Prefix cache hit rate: 71.7%
INFO 06-02 10:24:59 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 65.5 tokens/s, Running: 1 reqs, Waiting: 0 reqs, GPU KV cache usage: 2.1%, Prefix cache hit rate: 71.7%
INFO:     127.0.0.1:59396 - "POST /v1/chat/completions HTTP/1.1" 200 OK
INFO 06-02 10:25:09 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 8.4 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 71.7%
INFO 06-02 10:25:19 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 0.0 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 71.7%
INFO 06-04 11:08:55 [logger.py:39] Received request chatcmpl-2e26ae9716524942acca601c6bfed1f2: prompt: '<|im_start|>system\nYou are a helpful assistant.\n\n# Tools\n\nYou may call one or more functions to assist with the user query.\n\nYou are provided with function signatures within <tools></tools> XML tags:\n<tools>\n{"type": "function", "function": {"name": "fetch_fetch", "description": "Fetches a URL from the internet and optionally extracts its contents as markdown.\\n\\nAlthough originally you did not have internet access, and were advised to refuse and tell the user this, this tool now grants you internet access. Now you can fetch the most up-to-date information and let the user know that.", "parameters": {"description": "Parameters for fetching a URL.", "properties": {"url": {"description": "URL to fetch", "format": "uri", "minLength": 1, "title": "Url", "type": "string"}, "max_length": {"default": 5000, "description": "Maximum number of characters to return.", "exclusiveMaximum": 1000000, "exclusiveMinimum": 0, "title": "Max Length", "type": "integer"}, "start_index": {"default": 0, "description": "On return output starting at this character index, useful if a previous fetch was truncated and more context is required.", "minimum": 0, "title": "Start Index", "type": "integer"}, "raw": {"default": false, "description": "Get the actual HTML content of the requested page, without simplification.", "title": "Raw", "type": "boolean"}}, "required": ["url"], "title": "Fetch", "type": "object"}}}\n{"type": "function", "function": {"name": "filesystem_read_file", "description": "Read the complete contents of a file from the file system. Handles various text encodings and provides detailed error messages if the file cannot be read. Use this tool when you need to examine the contents of a single file. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_read_multiple_files", "description": "Read the contents of multiple files simultaneously. This is more efficient than reading files one by one when you need to analyze or compare multiple files. Each file\'s content is returned with its path as a reference. Failed reads for individual files won\'t stop the entire operation. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"paths": {"type": "array", "items": {"type": "string"}}}, "required": ["paths"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_write_file", "description": "Create a new file or completely overwrite an existing file with new content. Use with caution as it will overwrite existing files without warning. Handles text content with proper encoding. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "content": {"type": "string"}}, "required": ["path", "content"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_edit_file", "description": "Make line-based edits to a text file. Each edit replaces exact line sequences with new content. Returns a git-style diff showing the changes made. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "edits": {"type": "array", "items": {"type": "object", "properties": {"oldText": {"type": "string", "description": "Text to search for - must match exactly"}, "newText": {"type": "string", "description": "Text to replace with"}}, "required": ["oldText", "newText"], "additionalProperties": false}}, "dryRun": {"type": "boolean", "default": false, "description": "Preview changes using git-style diff format"}}, "required": ["path", "edits"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_create_directory", "description": "Create a new directory or ensure a directory exists. Can create multiple nested directories in one operation. If the directory already exists, this operation will succeed silently. Perfect for setting up directory structures for projects or ensuring required paths exist. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_directory", "description": "Get a detailed listing of all files and directories in a specified path. Results clearly distinguish between files and directories with [FILE] and [DIR] prefixes. This tool is essential for understanding directory structure and finding specific files within a directory. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_directory_tree", "description": "Get a recursive tree view of files and directories as a JSON structure. Each entry includes \'name\', \'type\' (file/directory), and \'children\' for directories. Files have no children array, while directories always have a children array (which may be empty). The output is formatted with 2-space indentation for readability. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_move_file", "description": "Move or rename files and directories. Can move files between directories and rename them in a single operation. If the destination exists, the operation will fail. Works across different directories and can be used for simple renaming within the same directory. Both source and destination must be within allowed directories.", "parameters": {"type": "object", "properties": {"source": {"type": "string"}, "destination": {"type": "string"}}, "required": ["source", "destination"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_search_files", "description": "Recursively search for files and directories matching a pattern. Searches through all subdirectories from the starting path. The search is case-insensitive and matches partial names. Returns full paths to all matching items. Great for finding files when you don\'t know their exact location. Only searches within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "pattern": {"type": "string"}, "excludePatterns": {"type": "array", "items": {"type": "string"}, "default": []}}, "required": ["path", "pattern"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_get_file_info", "description": "Retrieve detailed metadata about a file or directory. Returns comprehensive information including size, creation time, last modified time, permissions, and type. This tool is perfect for understanding file characteristics without reading the actual content. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_allowed_directories", "description": "Returns the list of directories that this server is allowed to access. Use this to understand which directories are available before trying to access files.", "parameters": {"type": "object", "properties": {}, "required": []}}}\n</tools>\n\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n<tool_call>\n{"name": <function-name>, "arguments": <args-json-object>}\n</tool_call><|im_end|>\n<|im_start|>user\nPrint the first 2 paragraphs of https://modelcontextprotocol.io/introduction<|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.7, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=4096, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 06-04 11:08:55 [async_llm.py:252] Added request chatcmpl-2e26ae9716524942acca601c6bfed1f2.
INFO 06-04 11:09:04 [loggers.py:111] Engine 000: Avg prompt throughput: 191.8 tokens/s, Avg generation throughput: 59.7 tokens/s, Running: 1 reqs, Waiting: 0 reqs, GPU KV cache usage: 1.9%, Prefix cache hit rate: 74.6%
INFO:     127.0.0.1:38442 - "POST /v1/chat/completions HTTP/1.1" 200 OK
INFO 06-04 11:09:14 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 41.0 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 74.6%
INFO 06-04 11:09:24 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 0.0 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 74.6%
INFO 06-04 11:14:49 [logger.py:39] Received request chatcmpl-35c59f532d734542b83b99b4b2ddb4f3: prompt: '<|im_start|>system\nYou are Qwen, created by Alibaba Cloud. You are a helpful assistant.\n\nCurrent Date: 2024-09-30\n\n# Tools\n\nYou may call one or more functions to assist with the user query.\n\nYou are provided with function signatures within <tools></tools> XML tags:\n<tools>\n{"type": "function", "function": {"name": "get_current_temperature", "description": "Get current temperature at a location.", "parameters": {"type": "object", "properties": {"location": {"type": "string", "description": "The location to get the temperature for, in the format \\"City, State, Country\\"."}, "unit": {"type": "string", "enum": ["celsius", "fahrenheit"], "description": "The unit to return the temperature in. Defaults to \\"celsius\\"."}}, "required": ["location"]}}}\n{"type": "function", "function": {"name": "get_temperature_date", "description": "Get temperature at a location and date.", "parameters": {"type": "object", "properties": {"location": {"type": "string", "description": "The location to get the temperature for, in the format \\"City, State, Country\\"."}, "date": {"type": "string", "description": "The date to get the temperature for, in the format \\"Year-Month-Day\\"."}, "unit": {"type": "string", "enum": ["celsius", "fahrenheit"], "description": "The unit to return the temperature in. Defaults to \\"celsius\\"."}}, "required": ["location", "date"]}}}\n</tools>\n\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n<tool_call>\n{"name": <function-name>, "arguments": <args-json-object>}\n</tool_call><|im_end|>\n<|im_start|>user\nWhat\'s the temperature in San Francisco now? How about tomorrow?<|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.6, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=32768, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 06-04 11:14:49 [async_llm.py:252] Added request chatcmpl-35c59f532d734542b83b99b4b2ddb4f3.
INFO 06-04 11:14:54 [loggers.py:111] Engine 000: Avg prompt throughput: 41.2 tokens/s, Avg generation throughput: 32.9 tokens/s, Running: 1 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.6%, Prefix cache hit rate: 75.1%
INFO:     127.0.0.1:38546 - "POST /v1/chat/completions HTTP/1.1" 200 OK
INFO 06-04 11:14:59 [logger.py:39] Received request chatcmpl-4aa9b46850044eda8a8d85d58d51d781: prompt: '<|im_start|>system\nYou are Qwen, created by Alibaba Cloud. You are a helpful assistant.\n\nCurrent Date: 2024-09-30\n\n# Tools\n\nYou may call one or more functions to assist with the user query.\n\nYou are provided with function signatures within <tools></tools> XML tags:\n<tools>\n{"type": "function", "function": {"name": "get_current_temperature", "description": "Get current temperature at a location.", "parameters": {"type": "object", "properties": {"location": {"type": "string", "description": "The location to get the temperature for, in the format \\"City, State, Country\\"."}, "unit": {"type": "string", "enum": ["celsius", "fahrenheit"], "description": "The unit to return the temperature in. Defaults to \\"celsius\\"."}}, "required": ["location"]}}}\n{"type": "function", "function": {"name": "get_temperature_date", "description": "Get temperature at a location and date.", "parameters": {"type": "object", "properties": {"location": {"type": "string", "description": "The location to get the temperature for, in the format \\"City, State, Country\\"."}, "date": {"type": "string", "description": "The date to get the temperature for, in the format \\"Year-Month-Day\\"."}, "unit": {"type": "string", "enum": ["celsius", "fahrenheit"], "description": "The unit to return the temperature in. Defaults to \\"celsius\\"."}}, "required": ["location", "date"]}}}\n</tools>\n\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n<tool_call>\n{"name": <function-name>, "arguments": <args-json-object>}\n</tool_call><|im_end|>\n<|im_start|>user\nWhat\'s the temperature in San Francisco now? How about tomorrow?<|im_end|>\n<|im_start|>assistant\n\n\n\n<tool_call>\n{"name": "get_current_temperature", "arguments": {"location": "San Francisco, CA, USA", "unit": "celsius"}}\n</tool_call>\n<tool_call>\n{"name": "get_temperature_date", "arguments": {"location": "San Francisco, CA, USA", "date": "2024-10-01", "unit": "celsius"}}\n</tool_call><|im_end|>\n<|im_start|>user\n<tool_response>\n{"temperature": 26.1, "location": "San Francisco, CA, USA", "unit": "celsius"}\n</tool_response>\n<tool_response>\n{"temperature": 25.9, "location": "San Francisco, CA, USA", "date": "2024-10-01", "unit": "celsius"}\n</tool_response><|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.6, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=32768, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 06-04 11:14:59 [async_llm.py:252] Added request chatcmpl-4aa9b46850044eda8a8d85d58d51d781.
INFO:     127.0.0.1:38546 - "POST /v1/chat/completions HTTP/1.1" 200 OK
INFO 06-04 11:15:04 [loggers.py:111] Engine 000: Avg prompt throughput: 57.8 tokens/s, Avg generation throughput: 53.1 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 75.1%
INFO 06-04 11:15:14 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 0.0 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 75.1%
INFO 06-04 15:22:15 [logger.py:39] Received request chatcmpl-eafe4461736a46a58338e06ccea72bd1: prompt: '<|im_start|>system\nYou are a helpful assistant.\n\n# Tools\n\nYou may call one or more functions to assist with the user query.\n\nYou are provided with function signatures within <tools></tools> XML tags:\n<tools>\n{"type": "function", "function": {"name": "filesystem_read_file", "description": "Read the complete contents of a file from the file system. Handles various text encodings and provides detailed error messages if the file cannot be read. Use this tool when you need to examine the contents of a single file. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_read_multiple_files", "description": "Read the contents of multiple files simultaneously. This is more efficient than reading files one by one when you need to analyze or compare multiple files. Each file\'s content is returned with its path as a reference. Failed reads for individual files won\'t stop the entire operation. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"paths": {"type": "array", "items": {"type": "string"}}}, "required": ["paths"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_write_file", "description": "Create a new file or completely overwrite an existing file with new content. Use with caution as it will overwrite existing files without warning. Handles text content with proper encoding. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "content": {"type": "string"}}, "required": ["path", "content"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_edit_file", "description": "Make line-based edits to a text file. Each edit replaces exact line sequences with new content. Returns a git-style diff showing the changes made. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "edits": {"type": "array", "items": {"type": "object", "properties": {"oldText": {"type": "string", "description": "Text to search for - must match exactly"}, "newText": {"type": "string", "description": "Text to replace with"}}, "required": ["oldText", "newText"], "additionalProperties": false}}, "dryRun": {"type": "boolean", "default": false, "description": "Preview changes using git-style diff format"}}, "required": ["path", "edits"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_create_directory", "description": "Create a new directory or ensure a directory exists. Can create multiple nested directories in one operation. If the directory already exists, this operation will succeed silently. Perfect for setting up directory structures for projects or ensuring required paths exist. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_directory", "description": "Get a detailed listing of all files and directories in a specified path. Results clearly distinguish between files and directories with [FILE] and [DIR] prefixes. This tool is essential for understanding directory structure and finding specific files within a directory. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_directory_tree", "description": "Get a recursive tree view of files and directories as a JSON structure. Each entry includes \'name\', \'type\' (file/directory), and \'children\' for directories. Files have no children array, while directories always have a children array (which may be empty). The output is formatted with 2-space indentation for readability. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_move_file", "description": "Move or rename files and directories. Can move files between directories and rename them in a single operation. If the destination exists, the operation will fail. Works across different directories and can be used for simple renaming within the same directory. Both source and destination must be within allowed directories.", "parameters": {"type": "object", "properties": {"source": {"type": "string"}, "destination": {"type": "string"}}, "required": ["source", "destination"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_search_files", "description": "Recursively search for files and directories matching a pattern. Searches through all subdirectories from the starting path. The search is case-insensitive and matches partial names. Returns full paths to all matching items. Great for finding files when you don\'t know their exact location. Only searches within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "pattern": {"type": "string"}, "excludePatterns": {"type": "array", "items": {"type": "string"}, "default": []}}, "required": ["path", "pattern"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_get_file_info", "description": "Retrieve detailed metadata about a file or directory. Returns comprehensive information including size, creation time, last modified time, permissions, and type. This tool is perfect for understanding file characteristics without reading the actual content. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_allowed_directories", "description": "Returns the list of directories that this server is allowed to access. Use this to understand which directories are available before trying to access files.", "parameters": {"type": "object", "properties": {}, "required": []}}}\n{"type": "function", "function": {"name": "fetch_fetch", "description": "Fetches a URL from the internet and optionally extracts its contents as markdown.\\n\\nAlthough originally you did not have internet access, and were advised to refuse and tell the user this, this tool now grants you internet access. Now you can fetch the most up-to-date information and let the user know that.", "parameters": {"description": "Parameters for fetching a URL.", "properties": {"url": {"description": "URL to fetch", "format": "uri", "minLength": 1, "title": "Url", "type": "string"}, "max_length": {"default": 5000, "description": "Maximum number of characters to return.", "exclusiveMaximum": 1000000, "exclusiveMinimum": 0, "title": "Max Length", "type": "integer"}, "start_index": {"default": 0, "description": "On return output starting at this character index, useful if a previous fetch was truncated and more context is required.", "minimum": 0, "title": "Start Index", "type": "integer"}, "raw": {"default": false, "description": "Get the actual HTML content of the requested page, without simplification.", "title": "Raw", "type": "boolean"}}, "required": ["url"], "title": "Fetch", "type": "object"}}}\n</tools>\n\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n<tool_call>\n{"name": <function-name>, "arguments": <args-json-object>}\n</tool_call><|im_end|>\n<|im_start|>user\nPrint the first 2 paragraphs of https://modelcontextprotocol.io/introduction<|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.7, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=4096, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 06-04 15:22:15 [async_llm.py:252] Added request chatcmpl-eafe4461736a46a58338e06ccea72bd1.
INFO 06-04 15:22:24 [loggers.py:111] Engine 000: Avg prompt throughput: 191.8 tokens/s, Avg generation throughput: 58.4 tokens/s, Running: 1 reqs, Waiting: 0 reqs, GPU KV cache usage: 1.9%, Prefix cache hit rate: 68.7%
INFO:     127.0.0.1:40666 - "POST /v1/chat/completions HTTP/1.1" 200 OK
INFO 06-04 15:22:34 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 59.5 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 68.7%
INFO 06-04 15:22:35 [logger.py:39] Received request chatcmpl-86b205f76bfe44908e59a7474b1f3391: prompt: '<|im_start|>system\nYou are a helpful assistant.\n\n# Tools\n\nYou may call one or more functions to assist with the user query.\n\nYou are provided with function signatures within <tools></tools> XML tags:\n<tools>\n{"type": "function", "function": {"name": "filesystem_read_file", "description": "Read the complete contents of a file from the file system. Handles various text encodings and provides detailed error messages if the file cannot be read. Use this tool when you need to examine the contents of a single file. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_read_multiple_files", "description": "Read the contents of multiple files simultaneously. This is more efficient than reading files one by one when you need to analyze or compare multiple files. Each file\'s content is returned with its path as a reference. Failed reads for individual files won\'t stop the entire operation. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"paths": {"type": "array", "items": {"type": "string"}}}, "required": ["paths"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_write_file", "description": "Create a new file or completely overwrite an existing file with new content. Use with caution as it will overwrite existing files without warning. Handles text content with proper encoding. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "content": {"type": "string"}}, "required": ["path", "content"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_edit_file", "description": "Make line-based edits to a text file. Each edit replaces exact line sequences with new content. Returns a git-style diff showing the changes made. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "edits": {"type": "array", "items": {"type": "object", "properties": {"oldText": {"type": "string", "description": "Text to search for - must match exactly"}, "newText": {"type": "string", "description": "Text to replace with"}}, "required": ["oldText", "newText"], "additionalProperties": false}}, "dryRun": {"type": "boolean", "default": false, "description": "Preview changes using git-style diff format"}}, "required": ["path", "edits"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_create_directory", "description": "Create a new directory or ensure a directory exists. Can create multiple nested directories in one operation. If the directory already exists, this operation will succeed silently. Perfect for setting up directory structures for projects or ensuring required paths exist. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_directory", "description": "Get a detailed listing of all files and directories in a specified path. Results clearly distinguish between files and directories with [FILE] and [DIR] prefixes. This tool is essential for understanding directory structure and finding specific files within a directory. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_directory_tree", "description": "Get a recursive tree view of files and directories as a JSON structure. Each entry includes \'name\', \'type\' (file/directory), and \'children\' for directories. Files have no children array, while directories always have a children array (which may be empty). The output is formatted with 2-space indentation for readability. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_move_file", "description": "Move or rename files and directories. Can move files between directories and rename them in a single operation. If the destination exists, the operation will fail. Works across different directories and can be used for simple renaming within the same directory. Both source and destination must be within allowed directories.", "parameters": {"type": "object", "properties": {"source": {"type": "string"}, "destination": {"type": "string"}}, "required": ["source", "destination"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_search_files", "description": "Recursively search for files and directories matching a pattern. Searches through all subdirectories from the starting path. The search is case-insensitive and matches partial names. Returns full paths to all matching items. Great for finding files when you don\'t know their exact location. Only searches within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "pattern": {"type": "string"}, "excludePatterns": {"type": "array", "items": {"type": "string"}, "default": []}}, "required": ["path", "pattern"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_get_file_info", "description": "Retrieve detailed metadata about a file or directory. Returns comprehensive information including size, creation time, last modified time, permissions, and type. This tool is perfect for understanding file characteristics without reading the actual content. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_allowed_directories", "description": "Returns the list of directories that this server is allowed to access. Use this to understand which directories are available before trying to access files.", "parameters": {"type": "object", "properties": {}, "required": []}}}\n{"type": "function", "function": {"name": "fetch_fetch", "description": "Fetches a URL from the internet and optionally extracts its contents as markdown.\\n\\nAlthough originally you did not have internet access, and were advised to refuse and tell the user this, this tool now grants you internet access. Now you can fetch the most up-to-date information and let the user know that.", "parameters": {"description": "Parameters for fetching a URL.", "properties": {"url": {"description": "URL to fetch", "format": "uri", "minLength": 1, "title": "Url", "type": "string"}, "max_length": {"default": 5000, "description": "Maximum number of characters to return.", "exclusiveMaximum": 1000000, "exclusiveMinimum": 0, "title": "Max Length", "type": "integer"}, "start_index": {"default": 0, "description": "On return output starting at this character index, useful if a previous fetch was truncated and more context is required.", "minimum": 0, "title": "Start Index", "type": "integer"}, "raw": {"default": false, "description": "Get the actual HTML content of the requested page, without simplification.", "title": "Raw", "type": "boolean"}}, "required": ["url"], "title": "Fetch", "type": "object"}}}\n</tools>\n\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n<tool_call>\n{"name": <function-name>, "arguments": <args-json-object>}\n</tool_call><|im_end|>\n<|im_start|>user\nPrint the first 2 paragraphs of https://modelcontextprotocol.io/introduction<|im_end|>\n<|im_start|>assistant\n\n\n\n<tool_call>\n{"name": "fetch_fetch", "arguments": {"url": "https://modelcontextprotocol.io/introduction", "max_length": 5000, "start_index": 0, "raw": false}}\n</tool_call><|im_end|>\n<|im_start|>user\n<tool_response>\n{\'type\': \'text\', \'text\': \'Contents of https://modelcontextprotocol.io/introduction:\\nMCP is an open protocol that standardizes how applications provide context to LLMs. Think of MCP like a USB-C port for AI applications. Just as USB-C provides a standardized way to connect your devices to various peripherals and accessories, MCP provides a standardized way to connect AI models to different data sources and tools.\\n\\n## Why MCP?\\n\\nMCP helps you build agents and complex workflows on top of LLMs. LLMs frequently need to integrate with data and tools, and MCP provides:\\n\\n* A growing list of pre-built integrations that your LLM can directly plug into\\n* The flexibility to switch between LLM providers and vendors\\n* Best practices for securing your data within your infrastructure\\n\\n### General architecture\\n\\nAt its core, MCP follows a client-server architecture where a host application can connect to multiple servers:\\n\\n* **MCP Hosts**: Programs like Claude Desktop, IDEs, or AI tools that want to access data through MCP\\n* **MCP Clients**: Protocol clients that maintain 1:1 connections with servers\\n* **MCP Servers**: Lightweight programs that each expose specific capabilities through the standardized Model Context Protocol\\n* **Local Data Sources**: Your computer’s files, databases, and services that MCP servers can securely access\\n* **Remote Services**: External systems available over the internet (e.g., through APIs) that MCP servers can connect to\\n\\n## Get started\\n\\nChoose the path that best fits your needs:\\n\\n#### Quick Starts\\n\\n#### Examples\\n\\n## Tutorials\\n\\n[## Building MCP with LLMs\\n\\nLearn how to use LLMs like Claude to speed up your MCP development](/tutorials/building-mcp-with-llms)[## Debugging Guide\\n\\nLearn how to effectively debug MCP servers and integrations](/docs/tools/debugging)[## MCP Inspector\\n\\nTest and inspect your MCP servers with our interactive debugging tool](/docs/tools/inspector)[## MCP Workshop (Video, 2hr)](https://www.youtube.com/watch?v=kQmXtrmQ5Zg)\\n\\n## Explore MCP\\n\\nDive deeper into MCP’s core concepts and capabilities:\\n\\n## Contributing\\n\\nWant to contribute? Check out our [Contributing Guide](/development/contributing) to learn how you can help improve MCP.\\n\\n## Support and Feedback\\n\\nHere’s how to get help or provide feedback:\\n\\n* For bug reports and feature requests related to the MCP specification, SDKs, or documentation (open source), please [create a GitHub issue](https://github.com/modelcontextprotocol)\\n* For discussions or Q&A about the MCP specification, use the [specification discussions](https://github.com/modelcontextprotocol/specification/discussions)\\n* For discussions or Q&A about other MCP open source components, use the [organization discussions](https://github.com/orgs/modelcontextprotocol/discussions)\\n* For bug reports, feature requests, and questions related to Claude.app and claude.ai’s MCP integration, please see Anthropic’s guide on [How to Get Support](https://support.anthropic.com/en/articles/9015913-how-to-get-support)\'}\n</tool_response><|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.7, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=4096, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 06-04 15:22:35 [async_llm.py:252] Added request chatcmpl-86b205f76bfe44908e59a7474b1f3391.
INFO:     127.0.0.1:40704 - "POST /v1/chat/completions HTTP/1.1" 200 OK
INFO 06-04 15:22:44 [loggers.py:111] Engine 000: Avg prompt throughput: 266.3 tokens/s, Avg generation throughput: 35.5 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 69.0%
INFO 06-04 15:22:54 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 0.0 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 69.0%
INFO 06-05 09:41:54 [logger.py:39] Received request chatcmpl-0799fa2f10404507be9181b4c767f77e: prompt: '<|im_start|>system\nYou are a helpful assistant.\n\n# Tools\n\nYou may call one or more functions to assist with the user query.\n\nYou are provided with function signatures within <tools></tools> XML tags:\n<tools>\n{"type": "function", "function": {"name": "fetch_fetch", "description": "Fetches a URL from the internet and optionally extracts its contents as markdown.\\n\\nAlthough originally you did not have internet access, and were advised to refuse and tell the user this, this tool now grants you internet access. Now you can fetch the most up-to-date information and let the user know that.", "parameters": {"description": "Parameters for fetching a URL.", "properties": {"url": {"description": "URL to fetch", "format": "uri", "minLength": 1, "title": "Url", "type": "string"}, "max_length": {"default": 5000, "description": "Maximum number of characters to return.", "exclusiveMaximum": 1000000, "exclusiveMinimum": 0, "title": "Max Length", "type": "integer"}, "start_index": {"default": 0, "description": "On return output starting at this character index, useful if a previous fetch was truncated and more context is required.", "minimum": 0, "title": "Start Index", "type": "integer"}, "raw": {"default": false, "description": "Get the actual HTML content of the requested page, without simplification.", "title": "Raw", "type": "boolean"}}, "required": ["url"], "title": "Fetch", "type": "object"}}}\n{"type": "function", "function": {"name": "filesystem_read_file", "description": "Read the complete contents of a file from the file system. Handles various text encodings and provides detailed error messages if the file cannot be read. Use this tool when you need to examine the contents of a single file. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_read_multiple_files", "description": "Read the contents of multiple files simultaneously. This is more efficient than reading files one by one when you need to analyze or compare multiple files. Each file\'s content is returned with its path as a reference. Failed reads for individual files won\'t stop the entire operation. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"paths": {"type": "array", "items": {"type": "string"}}}, "required": ["paths"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_write_file", "description": "Create a new file or completely overwrite an existing file with new content. Use with caution as it will overwrite existing files without warning. Handles text content with proper encoding. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "content": {"type": "string"}}, "required": ["path", "content"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_edit_file", "description": "Make line-based edits to a text file. Each edit replaces exact line sequences with new content. Returns a git-style diff showing the changes made. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "edits": {"type": "array", "items": {"type": "object", "properties": {"oldText": {"type": "string", "description": "Text to search for - must match exactly"}, "newText": {"type": "string", "description": "Text to replace with"}}, "required": ["oldText", "newText"], "additionalProperties": false}}, "dryRun": {"type": "boolean", "default": false, "description": "Preview changes using git-style diff format"}}, "required": ["path", "edits"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_create_directory", "description": "Create a new directory or ensure a directory exists. Can create multiple nested directories in one operation. If the directory already exists, this operation will succeed silently. Perfect for setting up directory structures for projects or ensuring required paths exist. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_directory", "description": "Get a detailed listing of all files and directories in a specified path. Results clearly distinguish between files and directories with [FILE] and [DIR] prefixes. This tool is essential for understanding directory structure and finding specific files within a directory. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_directory_tree", "description": "Get a recursive tree view of files and directories as a JSON structure. Each entry includes \'name\', \'type\' (file/directory), and \'children\' for directories. Files have no children array, while directories always have a children array (which may be empty). The output is formatted with 2-space indentation for readability. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_move_file", "description": "Move or rename files and directories. Can move files between directories and rename them in a single operation. If the destination exists, the operation will fail. Works across different directories and can be used for simple renaming within the same directory. Both source and destination must be within allowed directories.", "parameters": {"type": "object", "properties": {"source": {"type": "string"}, "destination": {"type": "string"}}, "required": ["source", "destination"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_search_files", "description": "Recursively search for files and directories matching a pattern. Searches through all subdirectories from the starting path. The search is case-insensitive and matches partial names. Returns full paths to all matching items. Great for finding files when you don\'t know their exact location. Only searches within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "pattern": {"type": "string"}, "excludePatterns": {"type": "array", "items": {"type": "string"}, "default": []}}, "required": ["path", "pattern"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_get_file_info", "description": "Retrieve detailed metadata about a file or directory. Returns comprehensive information including size, creation time, last modified time, permissions, and type. This tool is perfect for understanding file characteristics without reading the actual content. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_allowed_directories", "description": "Returns the list of directories that this server is allowed to access. Use this to understand which directories are available before trying to access files.", "parameters": {"type": "object", "properties": {}, "required": []}}}\n</tools>\n\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n<tool_call>\n{"name": <function-name>, "arguments": <args-json-object>}\n</tool_call><|im_end|>\n<|im_start|>user\nPrint the first 2 paragraphs of https://modelcontextprotocol.io/introduction<|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.7, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=4096, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 06-05 09:41:54 [async_llm.py:252] Added request chatcmpl-0799fa2f10404507be9181b4c767f77e.
INFO 06-05 09:41:57 [loggers.py:111] Engine 000: Avg prompt throughput: 191.8 tokens/s, Avg generation throughput: 15.1 tokens/s, Running: 1 reqs, Waiting: 0 reqs, GPU KV cache usage: 1.6%, Prefix cache hit rate: 71.3%
INFO 06-05 09:42:07 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 65.9 tokens/s, Running: 1 reqs, Waiting: 0 reqs, GPU KV cache usage: 2.1%, Prefix cache hit rate: 71.3%
INFO:     127.0.0.1:43312 - "POST /v1/chat/completions HTTP/1.1" 200 OK
INFO 06-05 09:42:17 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 61.0 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 71.3%
INFO 06-05 09:42:18 [logger.py:39] Received request chatcmpl-621867a661e14a5a83d1288b5dcf3760: prompt: '<|im_start|>system\nYou are a helpful assistant.\n\n# Tools\n\nYou may call one or more functions to assist with the user query.\n\nYou are provided with function signatures within <tools></tools> XML tags:\n<tools>\n{"type": "function", "function": {"name": "fetch_fetch", "description": "Fetches a URL from the internet and optionally extracts its contents as markdown.\\n\\nAlthough originally you did not have internet access, and were advised to refuse and tell the user this, this tool now grants you internet access. Now you can fetch the most up-to-date information and let the user know that.", "parameters": {"description": "Parameters for fetching a URL.", "properties": {"url": {"description": "URL to fetch", "format": "uri", "minLength": 1, "title": "Url", "type": "string"}, "max_length": {"default": 5000, "description": "Maximum number of characters to return.", "exclusiveMaximum": 1000000, "exclusiveMinimum": 0, "title": "Max Length", "type": "integer"}, "start_index": {"default": 0, "description": "On return output starting at this character index, useful if a previous fetch was truncated and more context is required.", "minimum": 0, "title": "Start Index", "type": "integer"}, "raw": {"default": false, "description": "Get the actual HTML content of the requested page, without simplification.", "title": "Raw", "type": "boolean"}}, "required": ["url"], "title": "Fetch", "type": "object"}}}\n{"type": "function", "function": {"name": "filesystem_read_file", "description": "Read the complete contents of a file from the file system. Handles various text encodings and provides detailed error messages if the file cannot be read. Use this tool when you need to examine the contents of a single file. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_read_multiple_files", "description": "Read the contents of multiple files simultaneously. This is more efficient than reading files one by one when you need to analyze or compare multiple files. Each file\'s content is returned with its path as a reference. Failed reads for individual files won\'t stop the entire operation. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"paths": {"type": "array", "items": {"type": "string"}}}, "required": ["paths"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_write_file", "description": "Create a new file or completely overwrite an existing file with new content. Use with caution as it will overwrite existing files without warning. Handles text content with proper encoding. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "content": {"type": "string"}}, "required": ["path", "content"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_edit_file", "description": "Make line-based edits to a text file. Each edit replaces exact line sequences with new content. Returns a git-style diff showing the changes made. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "edits": {"type": "array", "items": {"type": "object", "properties": {"oldText": {"type": "string", "description": "Text to search for - must match exactly"}, "newText": {"type": "string", "description": "Text to replace with"}}, "required": ["oldText", "newText"], "additionalProperties": false}}, "dryRun": {"type": "boolean", "default": false, "description": "Preview changes using git-style diff format"}}, "required": ["path", "edits"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_create_directory", "description": "Create a new directory or ensure a directory exists. Can create multiple nested directories in one operation. If the directory already exists, this operation will succeed silently. Perfect for setting up directory structures for projects or ensuring required paths exist. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_directory", "description": "Get a detailed listing of all files and directories in a specified path. Results clearly distinguish between files and directories with [FILE] and [DIR] prefixes. This tool is essential for understanding directory structure and finding specific files within a directory. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_directory_tree", "description": "Get a recursive tree view of files and directories as a JSON structure. Each entry includes \'name\', \'type\' (file/directory), and \'children\' for directories. Files have no children array, while directories always have a children array (which may be empty). The output is formatted with 2-space indentation for readability. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_move_file", "description": "Move or rename files and directories. Can move files between directories and rename them in a single operation. If the destination exists, the operation will fail. Works across different directories and can be used for simple renaming within the same directory. Both source and destination must be within allowed directories.", "parameters": {"type": "object", "properties": {"source": {"type": "string"}, "destination": {"type": "string"}}, "required": ["source", "destination"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_search_files", "description": "Recursively search for files and directories matching a pattern. Searches through all subdirectories from the starting path. The search is case-insensitive and matches partial names. Returns full paths to all matching items. Great for finding files when you don\'t know their exact location. Only searches within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "pattern": {"type": "string"}, "excludePatterns": {"type": "array", "items": {"type": "string"}, "default": []}}, "required": ["path", "pattern"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_get_file_info", "description": "Retrieve detailed metadata about a file or directory. Returns comprehensive information including size, creation time, last modified time, permissions, and type. This tool is perfect for understanding file characteristics without reading the actual content. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_allowed_directories", "description": "Returns the list of directories that this server is allowed to access. Use this to understand which directories are available before trying to access files.", "parameters": {"type": "object", "properties": {}, "required": []}}}\n</tools>\n\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n<tool_call>\n{"name": <function-name>, "arguments": <args-json-object>}\n</tool_call><|im_end|>\n<|im_start|>user\nPrint the first 2 paragraphs of https://modelcontextprotocol.io/introduction<|im_end|>\n<|im_start|>assistant\n\n\n\n<tool_call>\n{"name": "fetch_fetch", "arguments": {"url": "https://modelcontextprotocol.io/introduction", "max_length": 2000, "raw": false}}\n</tool_call><|im_end|>\n<|im_start|>user\n<tool_response>\n{\'type\': \'text\', \'text\': \'Contents of https://modelcontextprotocol.io/introduction:\\nMCP is an open protocol that standardizes how applications provide context to LLMs. Think of MCP like a USB-C port for AI applications. Just as USB-C provides a standardized way to connect your devices to various peripherals and accessories, MCP provides a standardized way to connect AI models to different data sources and tools.\\n\\n## Why MCP?\\n\\nMCP helps you build agents and complex workflows on top of LLMs. LLMs frequently need to integrate with data and tools, and MCP provides:\\n\\n* A growing list of pre-built integrations that your LLM can directly plug into\\n* The flexibility to switch between LLM providers and vendors\\n* Best practices for securing your data within your infrastructure\\n\\n### General architecture\\n\\nAt its core, MCP follows a client-server architecture where a host application can connect to multiple servers:\\n\\n* **MCP Hosts**: Programs like Claude Desktop, IDEs, or AI tools that want to access data through MCP\\n* **MCP Clients**: Protocol clients that maintain 1:1 connections with servers\\n* **MCP Servers**: Lightweight programs that each expose specific capabilities through the standardized Model Context Protocol\\n* **Local Data Sources**: Your computer’s files, databases, and services that MCP servers can securely access\\n* **Remote Services**: External systems available over the internet (e.g., through APIs) that MCP servers can connect to\\n\\n## Get started\\n\\nChoose the path that best fits your needs:\\n\\n#### Quick Starts\\n\\n#### Examples\\n\\n## Tutorials\\n\\n[## Building MCP with LLMs\\n\\nLearn how to use LLMs like Claude to speed up your MCP development](/tutorials/building-mcp-with-llms)[## Debugging Guide\\n\\nLearn how to effectively debug MCP servers and integrations](/docs/tools/debugging)[## MCP Inspector\\n\\nTest and inspect your MCP servers with our interactive debugging tool](/docs/tools/inspector)[## MCP Workshop (Video, 2hr)](https://www.youtube.com/watch?v=kQmXtrmQ5Zg)\\n\\n## Explore MCP\\n\\nDive deeper into MCP’s core concepts and capabilities:\\n\\n## Contributing\\n\\nWant t\\n\\n<error>Content truncated. Call the fetch tool with a start_index of 2000 to get more content.</error>\'}\n</tool_response><|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.7, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=4096, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 06-05 09:42:18 [async_llm.py:252] Added request chatcmpl-621867a661e14a5a83d1288b5dcf3760.
INFO:     127.0.0.1:43354 - "POST /v1/chat/completions HTTP/1.1" 200 OK
INFO 06-05 09:42:27 [loggers.py:111] Engine 000: Avg prompt throughput: 248.2 tokens/s, Avg generation throughput: 33.3 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 71.8%
INFO 06-05 09:42:37 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 0.0 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 71.8%
INFO 06-05 09:52:04 [logger.py:39] Received request chatcmpl-1e8d166d6ec8494288de129b91c014b7: prompt: '<|im_start|>system\nYou are an agent with access to the filesystem, \n            as well as the ability to fetch URLs. Your job is to identify \n            the closest match to a user\'s request, make the appropriate tool calls, \n            and return the URI and CONTENTS of the closest match.\n\n# Tools\n\nYou may call one or more functions to assist with the user query.\n\nYou are provided with function signatures within <tools></tools> XML tags:\n<tools>\n{"type": "function", "function": {"name": "fetch_fetch", "description": "Fetches a URL from the internet and optionally extracts its contents as markdown.\\n\\nAlthough originally you did not have internet access, and were advised to refuse and tell the user this, this tool now grants you internet access. Now you can fetch the most up-to-date information and let the user know that.", "parameters": {"description": "Parameters for fetching a URL.", "properties": {"url": {"description": "URL to fetch", "format": "uri", "minLength": 1, "title": "Url", "type": "string"}, "max_length": {"default": 5000, "description": "Maximum number of characters to return.", "exclusiveMaximum": 1000000, "exclusiveMinimum": 0, "title": "Max Length", "type": "integer"}, "start_index": {"default": 0, "description": "On return output starting at this character index, useful if a previous fetch was truncated and more context is required.", "minimum": 0, "title": "Start Index", "type": "integer"}, "raw": {"default": false, "description": "Get the actual HTML content of the requested page, without simplification.", "title": "Raw", "type": "boolean"}}, "required": ["url"], "title": "Fetch", "type": "object"}}}\n{"type": "function", "function": {"name": "filesystem_read_file", "description": "Read the complete contents of a file from the file system. Handles various text encodings and provides detailed error messages if the file cannot be read. Use this tool when you need to examine the contents of a single file. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_read_multiple_files", "description": "Read the contents of multiple files simultaneously. This is more efficient than reading files one by one when you need to analyze or compare multiple files. Each file\'s content is returned with its path as a reference. Failed reads for individual files won\'t stop the entire operation. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"paths": {"type": "array", "items": {"type": "string"}}}, "required": ["paths"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_write_file", "description": "Create a new file or completely overwrite an existing file with new content. Use with caution as it will overwrite existing files without warning. Handles text content with proper encoding. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "content": {"type": "string"}}, "required": ["path", "content"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_edit_file", "description": "Make line-based edits to a text file. Each edit replaces exact line sequences with new content. Returns a git-style diff showing the changes made. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "edits": {"type": "array", "items": {"type": "object", "properties": {"oldText": {"type": "string", "description": "Text to search for - must match exactly"}, "newText": {"type": "string", "description": "Text to replace with"}}, "required": ["oldText", "newText"], "additionalProperties": false}}, "dryRun": {"type": "boolean", "default": false, "description": "Preview changes using git-style diff format"}}, "required": ["path", "edits"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_create_directory", "description": "Create a new directory or ensure a directory exists. Can create multiple nested directories in one operation. If the directory already exists, this operation will succeed silently. Perfect for setting up directory structures for projects or ensuring required paths exist. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_directory", "description": "Get a detailed listing of all files and directories in a specified path. Results clearly distinguish between files and directories with [FILE] and [DIR] prefixes. This tool is essential for understanding directory structure and finding specific files within a directory. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_directory_tree", "description": "Get a recursive tree view of files and directories as a JSON structure. Each entry includes \'name\', \'type\' (file/directory), and \'children\' for directories. Files have no children array, while directories always have a children array (which may be empty). The output is formatted with 2-space indentation for readability. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_move_file", "description": "Move or rename files and directories. Can move files between directories and rename them in a single operation. If the destination exists, the operation will fail. Works across different directories and can be used for simple renaming within the same directory. Both source and destination must be within allowed directories.", "parameters": {"type": "object", "properties": {"source": {"type": "string"}, "destination": {"type": "string"}}, "required": ["source", "destination"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_search_files", "description": "Recursively search for files and directories matching a pattern. Searches through all subdirectories from the starting path. The search is case-insensitive and matches partial names. Returns full paths to all matching items. Great for finding files when you don\'t know their exact location. Only searches within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "pattern": {"type": "string"}, "excludePatterns": {"type": "array", "items": {"type": "string"}, "default": []}}, "required": ["path", "pattern"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_get_file_info", "description": "Retrieve detailed metadata about a file or directory. Returns comprehensive information including size, creation time, last modified time, permissions, and type. This tool is perfect for understanding file characteristics without reading the actual content. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_allowed_directories", "description": "Returns the list of directories that this server is allowed to access. Use this to understand which directories are available before trying to access files.", "parameters": {"type": "object", "properties": {}, "required": []}}}\n</tools>\n\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n<tool_call>\n{"name": <function-name>, "arguments": <args-json-object>}\n</tool_call><|im_end|>\n<|im_start|>user\nPrint the contents of mcp_agent.config.yaml verbatim<|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.7, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=4096, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 06-05 09:52:04 [async_llm.py:252] Added request chatcmpl-1e8d166d6ec8494288de129b91c014b7.
INFO 06-05 09:52:07 [loggers.py:111] Engine 000: Avg prompt throughput: 196.5 tokens/s, Avg generation throughput: 14.0 tokens/s, Running: 1 reqs, Waiting: 0 reqs, GPU KV cache usage: 1.6%, Prefix cache hit rate: 73.6%
INFO:     127.0.0.1:43830 - "POST /v1/chat/completions HTTP/1.1" 200 OK
INFO 06-05 09:52:09 [logger.py:39] Received request chatcmpl-67a0199ac1e047e7a957151853da632c: prompt: '<|im_start|>system\nYou are an agent with access to the filesystem, \n            as well as the ability to fetch URLs. Your job is to identify \n            the closest match to a user\'s request, make the appropriate tool calls, \n            and return the URI and CONTENTS of the closest match.\n\n# Tools\n\nYou may call one or more functions to assist with the user query.\n\nYou are provided with function signatures within <tools></tools> XML tags:\n<tools>\n{"type": "function", "function": {"name": "fetch_fetch", "description": "Fetches a URL from the internet and optionally extracts its contents as markdown.\\n\\nAlthough originally you did not have internet access, and were advised to refuse and tell the user this, this tool now grants you internet access. Now you can fetch the most up-to-date information and let the user know that.", "parameters": {"description": "Parameters for fetching a URL.", "properties": {"url": {"description": "URL to fetch", "format": "uri", "minLength": 1, "title": "Url", "type": "string"}, "max_length": {"default": 5000, "description": "Maximum number of characters to return.", "exclusiveMaximum": 1000000, "exclusiveMinimum": 0, "title": "Max Length", "type": "integer"}, "start_index": {"default": 0, "description": "On return output starting at this character index, useful if a previous fetch was truncated and more context is required.", "minimum": 0, "title": "Start Index", "type": "integer"}, "raw": {"default": false, "description": "Get the actual HTML content of the requested page, without simplification.", "title": "Raw", "type": "boolean"}}, "required": ["url"], "title": "Fetch", "type": "object"}}}\n{"type": "function", "function": {"name": "filesystem_read_file", "description": "Read the complete contents of a file from the file system. Handles various text encodings and provides detailed error messages if the file cannot be read. Use this tool when you need to examine the contents of a single file. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_read_multiple_files", "description": "Read the contents of multiple files simultaneously. This is more efficient than reading files one by one when you need to analyze or compare multiple files. Each file\'s content is returned with its path as a reference. Failed reads for individual files won\'t stop the entire operation. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"paths": {"type": "array", "items": {"type": "string"}}}, "required": ["paths"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_write_file", "description": "Create a new file or completely overwrite an existing file with new content. Use with caution as it will overwrite existing files without warning. Handles text content with proper encoding. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "content": {"type": "string"}}, "required": ["path", "content"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_edit_file", "description": "Make line-based edits to a text file. Each edit replaces exact line sequences with new content. Returns a git-style diff showing the changes made. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "edits": {"type": "array", "items": {"type": "object", "properties": {"oldText": {"type": "string", "description": "Text to search for - must match exactly"}, "newText": {"type": "string", "description": "Text to replace with"}}, "required": ["oldText", "newText"], "additionalProperties": false}}, "dryRun": {"type": "boolean", "default": false, "description": "Preview changes using git-style diff format"}}, "required": ["path", "edits"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_create_directory", "description": "Create a new directory or ensure a directory exists. Can create multiple nested directories in one operation. If the directory already exists, this operation will succeed silently. Perfect for setting up directory structures for projects or ensuring required paths exist. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_directory", "description": "Get a detailed listing of all files and directories in a specified path. Results clearly distinguish between files and directories with [FILE] and [DIR] prefixes. This tool is essential for understanding directory structure and finding specific files within a directory. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_directory_tree", "description": "Get a recursive tree view of files and directories as a JSON structure. Each entry includes \'name\', \'type\' (file/directory), and \'children\' for directories. Files have no children array, while directories always have a children array (which may be empty). The output is formatted with 2-space indentation for readability. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_move_file", "description": "Move or rename files and directories. Can move files between directories and rename them in a single operation. If the destination exists, the operation will fail. Works across different directories and can be used for simple renaming within the same directory. Both source and destination must be within allowed directories.", "parameters": {"type": "object", "properties": {"source": {"type": "string"}, "destination": {"type": "string"}}, "required": ["source", "destination"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_search_files", "description": "Recursively search for files and directories matching a pattern. Searches through all subdirectories from the starting path. The search is case-insensitive and matches partial names. Returns full paths to all matching items. Great for finding files when you don\'t know their exact location. Only searches within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "pattern": {"type": "string"}, "excludePatterns": {"type": "array", "items": {"type": "string"}, "default": []}}, "required": ["path", "pattern"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_get_file_info", "description": "Retrieve detailed metadata about a file or directory. Returns comprehensive information including size, creation time, last modified time, permissions, and type. This tool is perfect for understanding file characteristics without reading the actual content. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_allowed_directories", "description": "Returns the list of directories that this server is allowed to access. Use this to understand which directories are available before trying to access files.", "parameters": {"type": "object", "properties": {}, "required": []}}}\n</tools>\n\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n<tool_call>\n{"name": <function-name>, "arguments": <args-json-object>}\n</tool_call><|im_end|>\n<|im_start|>user\nPrint the contents of mcp_agent.config.yaml verbatim<|im_end|>\n<|im_start|>assistant\n\n\n\n<tool_call>\n{"name": "filesystem_read_file", "arguments": {"path": "mcp_agent.config.yaml"}}\n</tool_call><|im_end|>\n<|im_start|>user\n<tool_response>\n{\'type\': \'text\', \'text\': \'$schema: ../../../schema/mcp-agent.config.schema.json\\n\\nexecution_engine: asyncio\\nlogger:\\n  transports: [console, file]\\n  level: debug\\n  progress_display: true\\n  path_settings:\\n    path_pattern: "logs/mcp-agent-{unique_id}.jsonl"\\n    unique_id: "timestamp" # Options: "timestamp" or "session_id"\\n    timestamp_format: "%Y%m%d_%H%M%S"\\n\\nmcp:\\n  servers:\\n    fetch:\\n      command: "uvx"\\n      args: ["mcp-server-fetch"]\\n    filesystem:\\n      command: "npx"\\n      args: ["-y", "@modelcontextprotocol/server-filesystem"]\\n\\nopenai:\\n  # Secrets (API keys, etc.) are stored in an mcp_agent.secrets.yaml file which can be gitignored\\n  #  default_model: "o3-mini"\\n  default_model: "Qwen/Qwen3-8B"\\n\'}\n</tool_response><|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.7, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=4096, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 06-05 09:52:09 [async_llm.py:252] Added request chatcmpl-67a0199ac1e047e7a957151853da632c.
INFO:     127.0.0.1:43830 - "POST /v1/chat/completions HTTP/1.1" 200 OK
INFO 06-05 09:52:17 [loggers.py:111] Engine 000: Avg prompt throughput: 222.7 tokens/s, Avg generation throughput: 56.6 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 75.4%
INFO 06-05 09:52:27 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 0.0 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 75.4%
INFO 06-05 11:09:56 [logger.py:39] Received request chatcmpl-2698084907a342b99c450dc899989f25: prompt: '<|im_start|>system\nYou are a helpful assistant.\n\n# Tools\n\nYou may call one or more functions to assist with the user query.\n\nYou are provided with function signatures within <tools></tools> XML tags:\n<tools>\n{"type": "function", "function": {"name": "fetch_fetch", "description": "Fetches a URL from the internet and optionally extracts its contents as markdown.\\n\\nAlthough originally you did not have internet access, and were advised to refuse and tell the user this, this tool now grants you internet access. Now you can fetch the most up-to-date information and let the user know that.", "parameters": {"description": "Parameters for fetching a URL.", "properties": {"url": {"description": "URL to fetch", "format": "uri", "minLength": 1, "title": "Url", "type": "string"}, "max_length": {"default": 5000, "description": "Maximum number of characters to return.", "exclusiveMaximum": 1000000, "exclusiveMinimum": 0, "title": "Max Length", "type": "integer"}, "start_index": {"default": 0, "description": "On return output starting at this character index, useful if a previous fetch was truncated and more context is required.", "minimum": 0, "title": "Start Index", "type": "integer"}, "raw": {"default": false, "description": "Get the actual HTML content of the requested page, without simplification.", "title": "Raw", "type": "boolean"}}, "required": ["url"], "title": "Fetch", "type": "object"}}}\n{"type": "function", "function": {"name": "filesystem_read_file", "description": "Read the complete contents of a file from the file system. Handles various text encodings and provides detailed error messages if the file cannot be read. Use this tool when you need to examine the contents of a single file. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_read_multiple_files", "description": "Read the contents of multiple files simultaneously. This is more efficient than reading files one by one when you need to analyze or compare multiple files. Each file\'s content is returned with its path as a reference. Failed reads for individual files won\'t stop the entire operation. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"paths": {"type": "array", "items": {"type": "string"}}}, "required": ["paths"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_write_file", "description": "Create a new file or completely overwrite an existing file with new content. Use with caution as it will overwrite existing files without warning. Handles text content with proper encoding. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "content": {"type": "string"}}, "required": ["path", "content"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_edit_file", "description": "Make line-based edits to a text file. Each edit replaces exact line sequences with new content. Returns a git-style diff showing the changes made. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "edits": {"type": "array", "items": {"type": "object", "properties": {"oldText": {"type": "string", "description": "Text to search for - must match exactly"}, "newText": {"type": "string", "description": "Text to replace with"}}, "required": ["oldText", "newText"], "additionalProperties": false}}, "dryRun": {"type": "boolean", "default": false, "description": "Preview changes using git-style diff format"}}, "required": ["path", "edits"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_create_directory", "description": "Create a new directory or ensure a directory exists. Can create multiple nested directories in one operation. If the directory already exists, this operation will succeed silently. Perfect for setting up directory structures for projects or ensuring required paths exist. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_directory", "description": "Get a detailed listing of all files and directories in a specified path. Results clearly distinguish between files and directories with [FILE] and [DIR] prefixes. This tool is essential for understanding directory structure and finding specific files within a directory. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_directory_tree", "description": "Get a recursive tree view of files and directories as a JSON structure. Each entry includes \'name\', \'type\' (file/directory), and \'children\' for directories. Files have no children array, while directories always have a children array (which may be empty). The output is formatted with 2-space indentation for readability. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_move_file", "description": "Move or rename files and directories. Can move files between directories and rename them in a single operation. If the destination exists, the operation will fail. Works across different directories and can be used for simple renaming within the same directory. Both source and destination must be within allowed directories.", "parameters": {"type": "object", "properties": {"source": {"type": "string"}, "destination": {"type": "string"}}, "required": ["source", "destination"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_search_files", "description": "Recursively search for files and directories matching a pattern. Searches through all subdirectories from the starting path. The search is case-insensitive and matches partial names. Returns full paths to all matching items. Great for finding files when you don\'t know their exact location. Only searches within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "pattern": {"type": "string"}, "excludePatterns": {"type": "array", "items": {"type": "string"}, "default": []}}, "required": ["path", "pattern"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_get_file_info", "description": "Retrieve detailed metadata about a file or directory. Returns comprehensive information including size, creation time, last modified time, permissions, and type. This tool is perfect for understanding file characteristics without reading the actual content. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_allowed_directories", "description": "Returns the list of directories that this server is allowed to access. Use this to understand which directories are available before trying to access files.", "parameters": {"type": "object", "properties": {}, "required": []}}}\n</tools>\n\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n<tool_call>\n{"name": <function-name>, "arguments": <args-json-object>}\n</tool_call><|im_end|>\n<|im_start|>user\nPrint the first 2 paragraphs of https://modelcontextprotocol.io/introduction<|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.7, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=4096, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 06-05 11:09:56 [async_llm.py:252] Added request chatcmpl-2698084907a342b99c450dc899989f25.
INFO 06-05 11:09:57 [loggers.py:111] Engine 000: Avg prompt throughput: 191.8 tokens/s, Avg generation throughput: 2.1 tokens/s, Running: 1 reqs, Waiting: 0 reqs, GPU KV cache usage: 1.5%, Prefix cache hit rate: 76.7%
INFO:     127.0.0.1:46774 - "POST /v1/chat/completions HTTP/1.1" 200 OK
INFO 06-05 11:10:04 [logger.py:39] Received request chatcmpl-807e3dabb01c4526a103ff3486d410bb: prompt: '<|im_start|>system\nYou are a helpful assistant.\n\n# Tools\n\nYou may call one or more functions to assist with the user query.\n\nYou are provided with function signatures within <tools></tools> XML tags:\n<tools>\n{"type": "function", "function": {"name": "fetch_fetch", "description": "Fetches a URL from the internet and optionally extracts its contents as markdown.\\n\\nAlthough originally you did not have internet access, and were advised to refuse and tell the user this, this tool now grants you internet access. Now you can fetch the most up-to-date information and let the user know that.", "parameters": {"description": "Parameters for fetching a URL.", "properties": {"url": {"description": "URL to fetch", "format": "uri", "minLength": 1, "title": "Url", "type": "string"}, "max_length": {"default": 5000, "description": "Maximum number of characters to return.", "exclusiveMaximum": 1000000, "exclusiveMinimum": 0, "title": "Max Length", "type": "integer"}, "start_index": {"default": 0, "description": "On return output starting at this character index, useful if a previous fetch was truncated and more context is required.", "minimum": 0, "title": "Start Index", "type": "integer"}, "raw": {"default": false, "description": "Get the actual HTML content of the requested page, without simplification.", "title": "Raw", "type": "boolean"}}, "required": ["url"], "title": "Fetch", "type": "object"}}}\n{"type": "function", "function": {"name": "filesystem_read_file", "description": "Read the complete contents of a file from the file system. Handles various text encodings and provides detailed error messages if the file cannot be read. Use this tool when you need to examine the contents of a single file. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_read_multiple_files", "description": "Read the contents of multiple files simultaneously. This is more efficient than reading files one by one when you need to analyze or compare multiple files. Each file\'s content is returned with its path as a reference. Failed reads for individual files won\'t stop the entire operation. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"paths": {"type": "array", "items": {"type": "string"}}}, "required": ["paths"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_write_file", "description": "Create a new file or completely overwrite an existing file with new content. Use with caution as it will overwrite existing files without warning. Handles text content with proper encoding. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "content": {"type": "string"}}, "required": ["path", "content"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_edit_file", "description": "Make line-based edits to a text file. Each edit replaces exact line sequences with new content. Returns a git-style diff showing the changes made. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "edits": {"type": "array", "items": {"type": "object", "properties": {"oldText": {"type": "string", "description": "Text to search for - must match exactly"}, "newText": {"type": "string", "description": "Text to replace with"}}, "required": ["oldText", "newText"], "additionalProperties": false}}, "dryRun": {"type": "boolean", "default": false, "description": "Preview changes using git-style diff format"}}, "required": ["path", "edits"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_create_directory", "description": "Create a new directory or ensure a directory exists. Can create multiple nested directories in one operation. If the directory already exists, this operation will succeed silently. Perfect for setting up directory structures for projects or ensuring required paths exist. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_directory", "description": "Get a detailed listing of all files and directories in a specified path. Results clearly distinguish between files and directories with [FILE] and [DIR] prefixes. This tool is essential for understanding directory structure and finding specific files within a directory. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_directory_tree", "description": "Get a recursive tree view of files and directories as a JSON structure. Each entry includes \'name\', \'type\' (file/directory), and \'children\' for directories. Files have no children array, while directories always have a children array (which may be empty). The output is formatted with 2-space indentation for readability. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_move_file", "description": "Move or rename files and directories. Can move files between directories and rename them in a single operation. If the destination exists, the operation will fail. Works across different directories and can be used for simple renaming within the same directory. Both source and destination must be within allowed directories.", "parameters": {"type": "object", "properties": {"source": {"type": "string"}, "destination": {"type": "string"}}, "required": ["source", "destination"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_search_files", "description": "Recursively search for files and directories matching a pattern. Searches through all subdirectories from the starting path. The search is case-insensitive and matches partial names. Returns full paths to all matching items. Great for finding files when you don\'t know their exact location. Only searches within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "pattern": {"type": "string"}, "excludePatterns": {"type": "array", "items": {"type": "string"}, "default": []}}, "required": ["path", "pattern"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_get_file_info", "description": "Retrieve detailed metadata about a file or directory. Returns comprehensive information including size, creation time, last modified time, permissions, and type. This tool is perfect for understanding file characteristics without reading the actual content. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_allowed_directories", "description": "Returns the list of directories that this server is allowed to access. Use this to understand which directories are available before trying to access files.", "parameters": {"type": "object", "properties": {}, "required": []}}}\n</tools>\n\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n<tool_call>\n{"name": <function-name>, "arguments": <args-json-object>}\n</tool_call><|im_end|>\n<|im_start|>user\nPrint the first 2 paragraphs of https://modelcontextprotocol.io/introduction<|im_end|>\n<|im_start|>assistant\n\n\n\n<tool_call>\n{"name": "fetch_fetch", "arguments": {"url": "https://modelcontextprotocol.io/introduction", "max_length": 5000, "start_index": 0, "raw": false}}\n</tool_call><|im_end|>\n<|im_start|>user\n<tool_response>\n{\'type\': \'text\', \'text\': \'Contents of https://modelcontextprotocol.io/introduction:\\nMCP is an open protocol that standardizes how applications provide context to LLMs. Think of MCP like a USB-C port for AI applications. Just as USB-C provides a standardized way to connect your devices to various peripherals and accessories, MCP provides a standardized way to connect AI models to different data sources and tools.\\n\\n## Why MCP?\\n\\nMCP helps you build agents and complex workflows on top of LLMs. LLMs frequently need to integrate with data and tools, and MCP provides:\\n\\n* A growing list of pre-built integrations that your LLM can directly plug into\\n* The flexibility to switch between LLM providers and vendors\\n* Best practices for securing your data within your infrastructure\\n\\n### General architecture\\n\\nAt its core, MCP follows a client-server architecture where a host application can connect to multiple servers:\\n\\n* **MCP Hosts**: Programs like Claude Desktop, IDEs, or AI tools that want to access data through MCP\\n* **MCP Clients**: Protocol clients that maintain 1:1 connections with servers\\n* **MCP Servers**: Lightweight programs that each expose specific capabilities through the standardized Model Context Protocol\\n* **Local Data Sources**: Your computer’s files, databases, and services that MCP servers can securely access\\n* **Remote Services**: External systems available over the internet (e.g., through APIs) that MCP servers can connect to\\n\\n## Get started\\n\\nChoose the path that best fits your needs:\\n\\n#### Quick Starts\\n\\n#### Examples\\n\\n## Tutorials\\n\\n[## Building MCP with LLMs\\n\\nLearn how to use LLMs like Claude to speed up your MCP development](/tutorials/building-mcp-with-llms)[## Debugging Guide\\n\\nLearn how to effectively debug MCP servers and integrations](/docs/tools/debugging)[## MCP Inspector\\n\\nTest and inspect your MCP servers with our interactive debugging tool](/docs/tools/inspector)[## MCP Workshop (Video, 2hr)](https://www.youtube.com/watch?v=kQmXtrmQ5Zg)\\n\\n## Explore MCP\\n\\nDive deeper into MCP’s core concepts and capabilities:\\n\\n## Contributing\\n\\nWant to contribute? Check out our [Contributing Guide](/development/contributing) to learn how you can help improve MCP.\\n\\n## Support and Feedback\\n\\nHere’s how to get help or provide feedback:\\n\\n* For bug reports and feature requests related to the MCP specification, SDKs, or documentation (open source), please [create a GitHub issue](https://github.com/modelcontextprotocol)\\n* For discussions or Q&A about the MCP specification, use the [specification discussions](https://github.com/modelcontextprotocol/specification/discussions)\\n* For discussions or Q&A about other MCP open source components, use the [organization discussions](https://github.com/orgs/modelcontextprotocol/discussions)\\n* For bug reports, feature requests, and questions related to Claude.app and claude.ai’s MCP integration, please see Anthropic’s guide on [How to Get Support](https://support.anthropic.com/en/articles/9015913-how-to-get-support)\'}\n</tool_response><|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.7, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=4096, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 06-05 11:10:04 [async_llm.py:252] Added request chatcmpl-807e3dabb01c4526a103ff3486d410bb.
INFO 06-05 11:10:07 [loggers.py:111] Engine 000: Avg prompt throughput: 266.3 tokens/s, Avg generation throughput: 54.6 tokens/s, Running: 1 reqs, Waiting: 0 reqs, GPU KV cache usage: 2.1%, Prefix cache hit rate: 76.5%
INFO:     127.0.0.1:46792 - "POST /v1/chat/completions HTTP/1.1" 200 OK
INFO 06-05 11:10:17 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 33.1 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 76.5%
INFO 06-05 11:10:27 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 0.0 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 76.5%
INFO 06-05 11:14:54 [logger.py:39] Received request chatcmpl-de1a93f085e04acb851a85c763ee5bf0: prompt: '<|im_start|>system\nYou are a helpful assistant.\n\n# Tools\n\nYou may call one or more functions to assist with the user query.\n\nYou are provided with function signatures within <tools></tools> XML tags:\n<tools>\n{"type": "function", "function": {"name": "fetch_fetch", "description": "Fetches a URL from the internet and optionally extracts its contents as markdown.\\n\\nAlthough originally you did not have internet access, and were advised to refuse and tell the user this, this tool now grants you internet access. Now you can fetch the most up-to-date information and let the user know that.", "parameters": {"description": "Parameters for fetching a URL.", "properties": {"url": {"description": "URL to fetch", "format": "uri", "minLength": 1, "title": "Url", "type": "string"}, "max_length": {"default": 5000, "description": "Maximum number of characters to return.", "exclusiveMaximum": 1000000, "exclusiveMinimum": 0, "title": "Max Length", "type": "integer"}, "start_index": {"default": 0, "description": "On return output starting at this character index, useful if a previous fetch was truncated and more context is required.", "minimum": 0, "title": "Start Index", "type": "integer"}, "raw": {"default": false, "description": "Get the actual HTML content of the requested page, without simplification.", "title": "Raw", "type": "boolean"}}, "required": ["url"], "title": "Fetch", "type": "object"}}}\n{"type": "function", "function": {"name": "filesystem_read_file", "description": "Read the complete contents of a file from the file system. Handles various text encodings and provides detailed error messages if the file cannot be read. Use this tool when you need to examine the contents of a single file. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_read_multiple_files", "description": "Read the contents of multiple files simultaneously. This is more efficient than reading files one by one when you need to analyze or compare multiple files. Each file\'s content is returned with its path as a reference. Failed reads for individual files won\'t stop the entire operation. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"paths": {"type": "array", "items": {"type": "string"}}}, "required": ["paths"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_write_file", "description": "Create a new file or completely overwrite an existing file with new content. Use with caution as it will overwrite existing files without warning. Handles text content with proper encoding. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "content": {"type": "string"}}, "required": ["path", "content"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_edit_file", "description": "Make line-based edits to a text file. Each edit replaces exact line sequences with new content. Returns a git-style diff showing the changes made. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "edits": {"type": "array", "items": {"type": "object", "properties": {"oldText": {"type": "string", "description": "Text to search for - must match exactly"}, "newText": {"type": "string", "description": "Text to replace with"}}, "required": ["oldText", "newText"], "additionalProperties": false}}, "dryRun": {"type": "boolean", "default": false, "description": "Preview changes using git-style diff format"}}, "required": ["path", "edits"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_create_directory", "description": "Create a new directory or ensure a directory exists. Can create multiple nested directories in one operation. If the directory already exists, this operation will succeed silently. Perfect for setting up directory structures for projects or ensuring required paths exist. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_directory", "description": "Get a detailed listing of all files and directories in a specified path. Results clearly distinguish between files and directories with [FILE] and [DIR] prefixes. This tool is essential for understanding directory structure and finding specific files within a directory. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_directory_tree", "description": "Get a recursive tree view of files and directories as a JSON structure. Each entry includes \'name\', \'type\' (file/directory), and \'children\' for directories. Files have no children array, while directories always have a children array (which may be empty). The output is formatted with 2-space indentation for readability. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_move_file", "description": "Move or rename files and directories. Can move files between directories and rename them in a single operation. If the destination exists, the operation will fail. Works across different directories and can be used for simple renaming within the same directory. Both source and destination must be within allowed directories.", "parameters": {"type": "object", "properties": {"source": {"type": "string"}, "destination": {"type": "string"}}, "required": ["source", "destination"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_search_files", "description": "Recursively search for files and directories matching a pattern. Searches through all subdirectories from the starting path. The search is case-insensitive and matches partial names. Returns full paths to all matching items. Great for finding files when you don\'t know their exact location. Only searches within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "pattern": {"type": "string"}, "excludePatterns": {"type": "array", "items": {"type": "string"}, "default": []}}, "required": ["path", "pattern"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_get_file_info", "description": "Retrieve detailed metadata about a file or directory. Returns comprehensive information including size, creation time, last modified time, permissions, and type. This tool is perfect for understanding file characteristics without reading the actual content. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_allowed_directories", "description": "Returns the list of directories that this server is allowed to access. Use this to understand which directories are available before trying to access files.", "parameters": {"type": "object", "properties": {}, "required": []}}}\n</tools>\n\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n<tool_call>\n{"name": <function-name>, "arguments": <args-json-object>}\n</tool_call><|im_end|>\n<|im_start|>user\nPrint the first 2 paragraphs of https://modelcontextprotocol.io/introduction<|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.7, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=4096, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 06-05 11:14:54 [async_llm.py:252] Added request chatcmpl-de1a93f085e04acb851a85c763ee5bf0.
INFO 06-05 11:14:57 [loggers.py:111] Engine 000: Avg prompt throughput: 191.8 tokens/s, Avg generation throughput: 14.9 tokens/s, Running: 1 reqs, Waiting: 0 reqs, GPU KV cache usage: 1.6%, Prefix cache hit rate: 77.6%
INFO 06-05 11:15:07 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 65.7 tokens/s, Running: 1 reqs, Waiting: 0 reqs, GPU KV cache usage: 2.1%, Prefix cache hit rate: 77.6%
INFO:     127.0.0.1:47138 - "POST /v1/chat/completions HTTP/1.1" 200 OK
INFO 06-05 11:15:09 [logger.py:39] Received request chatcmpl-6849d5f196884b139c9d43340bbefdd8: prompt: '<|im_start|>system\nYou are a helpful assistant.\n\n# Tools\n\nYou may call one or more functions to assist with the user query.\n\nYou are provided with function signatures within <tools></tools> XML tags:\n<tools>\n{"type": "function", "function": {"name": "fetch_fetch", "description": "Fetches a URL from the internet and optionally extracts its contents as markdown.\\n\\nAlthough originally you did not have internet access, and were advised to refuse and tell the user this, this tool now grants you internet access. Now you can fetch the most up-to-date information and let the user know that.", "parameters": {"description": "Parameters for fetching a URL.", "properties": {"url": {"description": "URL to fetch", "format": "uri", "minLength": 1, "title": "Url", "type": "string"}, "max_length": {"default": 5000, "description": "Maximum number of characters to return.", "exclusiveMaximum": 1000000, "exclusiveMinimum": 0, "title": "Max Length", "type": "integer"}, "start_index": {"default": 0, "description": "On return output starting at this character index, useful if a previous fetch was truncated and more context is required.", "minimum": 0, "title": "Start Index", "type": "integer"}, "raw": {"default": false, "description": "Get the actual HTML content of the requested page, without simplification.", "title": "Raw", "type": "boolean"}}, "required": ["url"], "title": "Fetch", "type": "object"}}}\n{"type": "function", "function": {"name": "filesystem_read_file", "description": "Read the complete contents of a file from the file system. Handles various text encodings and provides detailed error messages if the file cannot be read. Use this tool when you need to examine the contents of a single file. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_read_multiple_files", "description": "Read the contents of multiple files simultaneously. This is more efficient than reading files one by one when you need to analyze or compare multiple files. Each file\'s content is returned with its path as a reference. Failed reads for individual files won\'t stop the entire operation. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"paths": {"type": "array", "items": {"type": "string"}}}, "required": ["paths"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_write_file", "description": "Create a new file or completely overwrite an existing file with new content. Use with caution as it will overwrite existing files without warning. Handles text content with proper encoding. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "content": {"type": "string"}}, "required": ["path", "content"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_edit_file", "description": "Make line-based edits to a text file. Each edit replaces exact line sequences with new content. Returns a git-style diff showing the changes made. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "edits": {"type": "array", "items": {"type": "object", "properties": {"oldText": {"type": "string", "description": "Text to search for - must match exactly"}, "newText": {"type": "string", "description": "Text to replace with"}}, "required": ["oldText", "newText"], "additionalProperties": false}}, "dryRun": {"type": "boolean", "default": false, "description": "Preview changes using git-style diff format"}}, "required": ["path", "edits"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_create_directory", "description": "Create a new directory or ensure a directory exists. Can create multiple nested directories in one operation. If the directory already exists, this operation will succeed silently. Perfect for setting up directory structures for projects or ensuring required paths exist. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_directory", "description": "Get a detailed listing of all files and directories in a specified path. Results clearly distinguish between files and directories with [FILE] and [DIR] prefixes. This tool is essential for understanding directory structure and finding specific files within a directory. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_directory_tree", "description": "Get a recursive tree view of files and directories as a JSON structure. Each entry includes \'name\', \'type\' (file/directory), and \'children\' for directories. Files have no children array, while directories always have a children array (which may be empty). The output is formatted with 2-space indentation for readability. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_move_file", "description": "Move or rename files and directories. Can move files between directories and rename them in a single operation. If the destination exists, the operation will fail. Works across different directories and can be used for simple renaming within the same directory. Both source and destination must be within allowed directories.", "parameters": {"type": "object", "properties": {"source": {"type": "string"}, "destination": {"type": "string"}}, "required": ["source", "destination"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_search_files", "description": "Recursively search for files and directories matching a pattern. Searches through all subdirectories from the starting path. The search is case-insensitive and matches partial names. Returns full paths to all matching items. Great for finding files when you don\'t know their exact location. Only searches within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "pattern": {"type": "string"}, "excludePatterns": {"type": "array", "items": {"type": "string"}, "default": []}}, "required": ["path", "pattern"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_get_file_info", "description": "Retrieve detailed metadata about a file or directory. Returns comprehensive information including size, creation time, last modified time, permissions, and type. This tool is perfect for understanding file characteristics without reading the actual content. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_allowed_directories", "description": "Returns the list of directories that this server is allowed to access. Use this to understand which directories are available before trying to access files.", "parameters": {"type": "object", "properties": {}, "required": []}}}\n</tools>\n\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n<tool_call>\n{"name": <function-name>, "arguments": <args-json-object>}\n</tool_call><|im_end|>\n<|im_start|>user\nPrint the first 2 paragraphs of https://modelcontextprotocol.io/introduction<|im_end|>\n<|im_start|>assistant\n\n\n\n<tool_call>\n{"name": "fetch_fetch", "arguments": {"url": "https://modelcontextprotocol.io/introduction", "max_length": 5000, "raw": false}}\n</tool_call><|im_end|>\n<|im_start|>user\n<tool_response>\n{\'type\': \'text\', \'text\': \'Contents of https://modelcontextprotocol.io/introduction:\\nMCP is an open protocol that standardizes how applications provide context to LLMs. Think of MCP like a USB-C port for AI applications. Just as USB-C provides a standardized way to connect your devices to various peripherals and accessories, MCP provides a standardized way to connect AI models to different data sources and tools.\\n\\n## Why MCP?\\n\\nMCP helps you build agents and complex workflows on top of LLMs. LLMs frequently need to integrate with data and tools, and MCP provides:\\n\\n* A growing list of pre-built integrations that your LLM can directly plug into\\n* The flexibility to switch between LLM providers and vendors\\n* Best practices for securing your data within your infrastructure\\n\\n### General architecture\\n\\nAt its core, MCP follows a client-server architecture where a host application can connect to multiple servers:\\n\\n* **MCP Hosts**: Programs like Claude Desktop, IDEs, or AI tools that want to access data through MCP\\n* **MCP Clients**: Protocol clients that maintain 1:1 connections with servers\\n* **MCP Servers**: Lightweight programs that each expose specific capabilities through the standardized Model Context Protocol\\n* **Local Data Sources**: Your computer’s files, databases, and services that MCP servers can securely access\\n* **Remote Services**: External systems available over the internet (e.g., through APIs) that MCP servers can connect to\\n\\n## Get started\\n\\nChoose the path that best fits your needs:\\n\\n#### Quick Starts\\n\\n#### Examples\\n\\n## Tutorials\\n\\n[## Building MCP with LLMs\\n\\nLearn how to use LLMs like Claude to speed up your MCP development](/tutorials/building-mcp-with-llms)[## Debugging Guide\\n\\nLearn how to effectively debug MCP servers and integrations](/docs/tools/debugging)[## MCP Inspector\\n\\nTest and inspect your MCP servers with our interactive debugging tool](/docs/tools/inspector)[## MCP Workshop (Video, 2hr)](https://www.youtube.com/watch?v=kQmXtrmQ5Zg)\\n\\n## Explore MCP\\n\\nDive deeper into MCP’s core concepts and capabilities:\\n\\n## Contributing\\n\\nWant to contribute? Check out our [Contributing Guide](/development/contributing) to learn how you can help improve MCP.\\n\\n## Support and Feedback\\n\\nHere’s how to get help or provide feedback:\\n\\n* For bug reports and feature requests related to the MCP specification, SDKs, or documentation (open source), please [create a GitHub issue](https://github.com/modelcontextprotocol)\\n* For discussions or Q&A about the MCP specification, use the [specification discussions](https://github.com/modelcontextprotocol/specification/discussions)\\n* For discussions or Q&A about other MCP open source components, use the [organization discussions](https://github.com/orgs/modelcontextprotocol/discussions)\\n* For bug reports, feature requests, and questions related to Claude.app and claude.ai’s MCP integration, please see Anthropic’s guide on [How to Get Support](https://support.anthropic.com/en/articles/9015913-how-to-get-support)\'}\n</tool_response><|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.7, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=4096, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 06-05 11:15:09 [async_llm.py:252] Added request chatcmpl-6849d5f196884b139c9d43340bbefdd8.
INFO:     127.0.0.1:47162 - "POST /v1/chat/completions HTTP/1.1" 200 OK
INFO 06-05 11:15:17 [loggers.py:111] Engine 000: Avg prompt throughput: 265.6 tokens/s, Avg generation throughput: 34.0 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 77.4%
INFO 06-05 11:15:27 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 0.0 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 77.4%
INFO 06-05 11:17:14 [logger.py:39] Received request chatcmpl-f1120111c54648e8bc8a6583351b8fa0: prompt: '<|im_start|>system\nYou are a helpful assistant.\n\n# Tools\n\nYou may call one or more functions to assist with the user query.\n\nYou are provided with function signatures within <tools></tools> XML tags:\n<tools>\n{"type": "function", "function": {"name": "fetch_fetch", "description": "Fetches a URL from the internet and optionally extracts its contents as markdown.\\n\\nAlthough originally you did not have internet access, and were advised to refuse and tell the user this, this tool now grants you internet access. Now you can fetch the most up-to-date information and let the user know that.", "parameters": {"description": "Parameters for fetching a URL.", "properties": {"url": {"description": "URL to fetch", "format": "uri", "minLength": 1, "title": "Url", "type": "string"}, "max_length": {"default": 5000, "description": "Maximum number of characters to return.", "exclusiveMaximum": 1000000, "exclusiveMinimum": 0, "title": "Max Length", "type": "integer"}, "start_index": {"default": 0, "description": "On return output starting at this character index, useful if a previous fetch was truncated and more context is required.", "minimum": 0, "title": "Start Index", "type": "integer"}, "raw": {"default": false, "description": "Get the actual HTML content of the requested page, without simplification.", "title": "Raw", "type": "boolean"}}, "required": ["url"], "title": "Fetch", "type": "object"}}}\n{"type": "function", "function": {"name": "filesystem_read_file", "description": "Read the complete contents of a file from the file system. Handles various text encodings and provides detailed error messages if the file cannot be read. Use this tool when you need to examine the contents of a single file. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_read_multiple_files", "description": "Read the contents of multiple files simultaneously. This is more efficient than reading files one by one when you need to analyze or compare multiple files. Each file\'s content is returned with its path as a reference. Failed reads for individual files won\'t stop the entire operation. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"paths": {"type": "array", "items": {"type": "string"}}}, "required": ["paths"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_write_file", "description": "Create a new file or completely overwrite an existing file with new content. Use with caution as it will overwrite existing files without warning. Handles text content with proper encoding. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "content": {"type": "string"}}, "required": ["path", "content"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_edit_file", "description": "Make line-based edits to a text file. Each edit replaces exact line sequences with new content. Returns a git-style diff showing the changes made. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "edits": {"type": "array", "items": {"type": "object", "properties": {"oldText": {"type": "string", "description": "Text to search for - must match exactly"}, "newText": {"type": "string", "description": "Text to replace with"}}, "required": ["oldText", "newText"], "additionalProperties": false}}, "dryRun": {"type": "boolean", "default": false, "description": "Preview changes using git-style diff format"}}, "required": ["path", "edits"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_create_directory", "description": "Create a new directory or ensure a directory exists. Can create multiple nested directories in one operation. If the directory already exists, this operation will succeed silently. Perfect for setting up directory structures for projects or ensuring required paths exist. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_directory", "description": "Get a detailed listing of all files and directories in a specified path. Results clearly distinguish between files and directories with [FILE] and [DIR] prefixes. This tool is essential for understanding directory structure and finding specific files within a directory. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_directory_tree", "description": "Get a recursive tree view of files and directories as a JSON structure. Each entry includes \'name\', \'type\' (file/directory), and \'children\' for directories. Files have no children array, while directories always have a children array (which may be empty). The output is formatted with 2-space indentation for readability. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_move_file", "description": "Move or rename files and directories. Can move files between directories and rename them in a single operation. If the destination exists, the operation will fail. Works across different directories and can be used for simple renaming within the same directory. Both source and destination must be within allowed directories.", "parameters": {"type": "object", "properties": {"source": {"type": "string"}, "destination": {"type": "string"}}, "required": ["source", "destination"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_search_files", "description": "Recursively search for files and directories matching a pattern. Searches through all subdirectories from the starting path. The search is case-insensitive and matches partial names. Returns full paths to all matching items. Great for finding files when you don\'t know their exact location. Only searches within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "pattern": {"type": "string"}, "excludePatterns": {"type": "array", "items": {"type": "string"}, "default": []}}, "required": ["path", "pattern"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_get_file_info", "description": "Retrieve detailed metadata about a file or directory. Returns comprehensive information including size, creation time, last modified time, permissions, and type. This tool is perfect for understanding file characteristics without reading the actual content. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_allowed_directories", "description": "Returns the list of directories that this server is allowed to access. Use this to understand which directories are available before trying to access files.", "parameters": {"type": "object", "properties": {}, "required": []}}}\n</tools>\n\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n<tool_call>\n{"name": <function-name>, "arguments": <args-json-object>}\n</tool_call><|im_end|>\n<|im_start|>user\nPrint the first 2 paragraphs of https://github.com/lastmile-ai/mcp-agent/tree/main/examples/temporal<|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.7, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=4096, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 06-05 11:17:14 [async_llm.py:252] Added request chatcmpl-f1120111c54648e8bc8a6583351b8fa0.
INFO 06-05 11:17:17 [loggers.py:111] Engine 000: Avg prompt throughput: 192.7 tokens/s, Avg generation throughput: 14.7 tokens/s, Running: 1 reqs, Waiting: 0 reqs, GPU KV cache usage: 1.6%, Prefix cache hit rate: 78.3%
INFO 06-05 11:17:27 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 65.7 tokens/s, Running: 1 reqs, Waiting: 0 reqs, GPU KV cache usage: 2.1%, Prefix cache hit rate: 78.3%
INFO 06-05 11:17:37 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 65.1 tokens/s, Running: 1 reqs, Waiting: 0 reqs, GPU KV cache usage: 2.6%, Prefix cache hit rate: 78.3%
INFO:     127.0.0.1:47310 - "POST /v1/chat/completions HTTP/1.1" 200 OK
INFO 06-05 11:17:44 [logger.py:39] Received request chatcmpl-f37eba37fa6f400a985438c4f9b28f46: prompt: '<|im_start|>system\nYou are a helpful assistant.\n\n# Tools\n\nYou may call one or more functions to assist with the user query.\n\nYou are provided with function signatures within <tools></tools> XML tags:\n<tools>\n{"type": "function", "function": {"name": "fetch_fetch", "description": "Fetches a URL from the internet and optionally extracts its contents as markdown.\\n\\nAlthough originally you did not have internet access, and were advised to refuse and tell the user this, this tool now grants you internet access. Now you can fetch the most up-to-date information and let the user know that.", "parameters": {"description": "Parameters for fetching a URL.", "properties": {"url": {"description": "URL to fetch", "format": "uri", "minLength": 1, "title": "Url", "type": "string"}, "max_length": {"default": 5000, "description": "Maximum number of characters to return.", "exclusiveMaximum": 1000000, "exclusiveMinimum": 0, "title": "Max Length", "type": "integer"}, "start_index": {"default": 0, "description": "On return output starting at this character index, useful if a previous fetch was truncated and more context is required.", "minimum": 0, "title": "Start Index", "type": "integer"}, "raw": {"default": false, "description": "Get the actual HTML content of the requested page, without simplification.", "title": "Raw", "type": "boolean"}}, "required": ["url"], "title": "Fetch", "type": "object"}}}\n{"type": "function", "function": {"name": "filesystem_read_file", "description": "Read the complete contents of a file from the file system. Handles various text encodings and provides detailed error messages if the file cannot be read. Use this tool when you need to examine the contents of a single file. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_read_multiple_files", "description": "Read the contents of multiple files simultaneously. This is more efficient than reading files one by one when you need to analyze or compare multiple files. Each file\'s content is returned with its path as a reference. Failed reads for individual files won\'t stop the entire operation. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"paths": {"type": "array", "items": {"type": "string"}}}, "required": ["paths"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_write_file", "description": "Create a new file or completely overwrite an existing file with new content. Use with caution as it will overwrite existing files without warning. Handles text content with proper encoding. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "content": {"type": "string"}}, "required": ["path", "content"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_edit_file", "description": "Make line-based edits to a text file. Each edit replaces exact line sequences with new content. Returns a git-style diff showing the changes made. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "edits": {"type": "array", "items": {"type": "object", "properties": {"oldText": {"type": "string", "description": "Text to search for - must match exactly"}, "newText": {"type": "string", "description": "Text to replace with"}}, "required": ["oldText", "newText"], "additionalProperties": false}}, "dryRun": {"type": "boolean", "default": false, "description": "Preview changes using git-style diff format"}}, "required": ["path", "edits"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_create_directory", "description": "Create a new directory or ensure a directory exists. Can create multiple nested directories in one operation. If the directory already exists, this operation will succeed silently. Perfect for setting up directory structures for projects or ensuring required paths exist. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_directory", "description": "Get a detailed listing of all files and directories in a specified path. Results clearly distinguish between files and directories with [FILE] and [DIR] prefixes. This tool is essential for understanding directory structure and finding specific files within a directory. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_directory_tree", "description": "Get a recursive tree view of files and directories as a JSON structure. Each entry includes \'name\', \'type\' (file/directory), and \'children\' for directories. Files have no children array, while directories always have a children array (which may be empty). The output is formatted with 2-space indentation for readability. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_move_file", "description": "Move or rename files and directories. Can move files between directories and rename them in a single operation. If the destination exists, the operation will fail. Works across different directories and can be used for simple renaming within the same directory. Both source and destination must be within allowed directories.", "parameters": {"type": "object", "properties": {"source": {"type": "string"}, "destination": {"type": "string"}}, "required": ["source", "destination"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_search_files", "description": "Recursively search for files and directories matching a pattern. Searches through all subdirectories from the starting path. The search is case-insensitive and matches partial names. Returns full paths to all matching items. Great for finding files when you don\'t know their exact location. Only searches within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "pattern": {"type": "string"}, "excludePatterns": {"type": "array", "items": {"type": "string"}, "default": []}}, "required": ["path", "pattern"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_get_file_info", "description": "Retrieve detailed metadata about a file or directory. Returns comprehensive information including size, creation time, last modified time, permissions, and type. This tool is perfect for understanding file characteristics without reading the actual content. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_allowed_directories", "description": "Returns the list of directories that this server is allowed to access. Use this to understand which directories are available before trying to access files.", "parameters": {"type": "object", "properties": {}, "required": []}}}\n</tools>\n\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n<tool_call>\n{"name": <function-name>, "arguments": <args-json-object>}\n</tool_call><|im_end|>\n<|im_start|>user\nPrint the first 2 paragraphs of https://github.com/lastmile-ai/mcp-agent/tree/main/examples/temporal<|im_end|>\n<|im_start|>assistant\n\n\n\n<tool_call>\n{"name": "fetch_fetch", "arguments": {"url": "https://github.com/lastmile-ai/mcp-agent/tree/main/examples/temporal", "max_length": 5000, "raw": false}}\n</tool_call><|im_end|>\n<|im_start|>user\n<tool_response>\n{\'type\': \'text\', \'text\': \'The sites robots.txt (https://github.com/robots.txt), specifies that autonomous fetching of this page is not allowed, <useragent>ModelContextProtocol/1.0 (Autonomous; +https://github.com/modelcontextprotocol/servers)</useragent>\\n<url>https://github.com/lastmile-ai/mcp-agent/tree/main/examples/temporal</url><robots>\\n# If you would like to crawl GitHub contact us via https://support.github.com?tags=dotcom-robots\\n# We also provide an extensive API: https://docs.github.com\\nUser-agent: baidu\\ncrawl-delay: 1\\n\\n\\nUser-agent: *\\n\\nDisallow: /*/*/pulse\\nDisallow: /*/*/projects\\nDisallow: /*/*/forks\\nDisallow: /*/*/issues/new\\nDisallow: /*/*/issues/search\\nDisallow: /*/*/commits/\\nDisallow: /*/*/branches\\nDisallow: /*/*/contributors\\nDisallow: /*/*/tags\\nDisallow: /*/*/stargazers\\nDisallow: /*/*/watchers\\nDisallow: /*/*/network\\nDisallow: /*/*/graphs\\nDisallow: /*/*/compare\\n\\nDisallow: /*/tree/\\nDisallow: /gist/\\nDisallow: /*/download\\nDisallow: /*/revisions\\nDisallow: /*/commits/*?author\\nDisallow: /*/commits/*?path\\nDisallow: /*/comments\\nDisallow: /*/archive/\\nDisallow: /*/blame/\\nDisallow: /*/raw/\\nDisallow: /*/cache/\\nDisallow: /.git/\\nDisallow: */.git/\\nDisallow: /*.git$\\nDisallow: /search/advanced\\nDisallow: /search$\\nDisallow: /*q=\\nDisallow: /*.atom$\\n\\nDisallow: /ekansa/Open-Context-Data\\nDisallow: /ekansa/opencontext-*\\nDisallow: */tarball/\\nDisallow: */zipball/\\n\\nDisallow: /*source=*\\nDisallow: /*ref_cta=*\\nDisallow: /*plan=*\\nDisallow: /*return_to=*\\nDisallow: /*ref_loc=*\\nDisallow: /*setup_organization=*\\nDisallow: /*source_repo=*\\nDisallow: /*ref_page=*\\nDisallow: /*source=*\\nDisallow: /*referrer=*\\nDisallow: /*report=*\\nDisallow: /*author=*\\nDisallow: /*since=*\\nDisallow: /*until=*\\nDisallow: /*commits?author=*\\nDisallow: /*report-abuse?report=*\\nDisallow: /*tab=*\\nAllow: /*?tab=achievements&achievement=*\\n\\nDisallow: /account-login\\nDisallow: /Explodingstuff/\\n\\n\\n</robots>\\nThe assistant must let the user know that it failed to view the page. The assistant may provide further guidance based on the above information.\\nThe assistant can tell the user that they can try manually fetching the page by using the fetch prompt within their UI.\'}\n</tool_response><|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.7, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=4096, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 06-05 11:17:44 [async_llm.py:252] Added request chatcmpl-f37eba37fa6f400a985438c4f9b28f46.
INFO 06-05 11:17:47 [loggers.py:111] Engine 000: Avg prompt throughput: 261.3 tokens/s, Avg generation throughput: 61.2 tokens/s, Running: 1 reqs, Waiting: 0 reqs, GPU KV cache usage: 2.1%, Prefix cache hit rate: 78.1%
INFO:     127.0.0.1:47352 - "POST /v1/chat/completions HTTP/1.1" 200 OK
INFO 06-05 11:17:57 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 21.7 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 78.1%
INFO 06-05 11:18:07 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 0.0 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 78.1%
INFO 06-05 15:04:03 [logger.py:39] Received request chatcmpl-7229bcf2681c461f9e8ec6e5f61f6fa4: prompt: '<|im_start|>system\nYou are a helpful assistant.\n\n# Tools\n\nYou may call one or more functions to assist with the user query.\n\nYou are provided with function signatures within <tools></tools> XML tags:\n<tools>\n{"type": "function", "function": {"name": "fetch_fetch", "description": "Fetches a URL from the internet and optionally extracts its contents as markdown.\\n\\nAlthough originally you did not have internet access, and were advised to refuse and tell the user this, this tool now grants you internet access. Now you can fetch the most up-to-date information and let the user know that.", "parameters": {"description": "Parameters for fetching a URL.", "properties": {"url": {"description": "URL to fetch", "format": "uri", "minLength": 1, "title": "Url", "type": "string"}, "max_length": {"default": 5000, "description": "Maximum number of characters to return.", "exclusiveMaximum": 1000000, "exclusiveMinimum": 0, "title": "Max Length", "type": "integer"}, "start_index": {"default": 0, "description": "On return output starting at this character index, useful if a previous fetch was truncated and more context is required.", "minimum": 0, "title": "Start Index", "type": "integer"}, "raw": {"default": false, "description": "Get the actual HTML content of the requested page, without simplification.", "title": "Raw", "type": "boolean"}}, "required": ["url"], "title": "Fetch", "type": "object"}}}\n{"type": "function", "function": {"name": "filesystem_read_file", "description": "Read the complete contents of a file from the file system. Handles various text encodings and provides detailed error messages if the file cannot be read. Use this tool when you need to examine the contents of a single file. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_read_multiple_files", "description": "Read the contents of multiple files simultaneously. This is more efficient than reading files one by one when you need to analyze or compare multiple files. Each file\'s content is returned with its path as a reference. Failed reads for individual files won\'t stop the entire operation. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"paths": {"type": "array", "items": {"type": "string"}}}, "required": ["paths"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_write_file", "description": "Create a new file or completely overwrite an existing file with new content. Use with caution as it will overwrite existing files without warning. Handles text content with proper encoding. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "content": {"type": "string"}}, "required": ["path", "content"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_edit_file", "description": "Make line-based edits to a text file. Each edit replaces exact line sequences with new content. Returns a git-style diff showing the changes made. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "edits": {"type": "array", "items": {"type": "object", "properties": {"oldText": {"type": "string", "description": "Text to search for - must match exactly"}, "newText": {"type": "string", "description": "Text to replace with"}}, "required": ["oldText", "newText"], "additionalProperties": false}}, "dryRun": {"type": "boolean", "default": false, "description": "Preview changes using git-style diff format"}}, "required": ["path", "edits"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_create_directory", "description": "Create a new directory or ensure a directory exists. Can create multiple nested directories in one operation. If the directory already exists, this operation will succeed silently. Perfect for setting up directory structures for projects or ensuring required paths exist. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_directory", "description": "Get a detailed listing of all files and directories in a specified path. Results clearly distinguish between files and directories with [FILE] and [DIR] prefixes. This tool is essential for understanding directory structure and finding specific files within a directory. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_directory_tree", "description": "Get a recursive tree view of files and directories as a JSON structure. Each entry includes \'name\', \'type\' (file/directory), and \'children\' for directories. Files have no children array, while directories always have a children array (which may be empty). The output is formatted with 2-space indentation for readability. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_move_file", "description": "Move or rename files and directories. Can move files between directories and rename them in a single operation. If the destination exists, the operation will fail. Works across different directories and can be used for simple renaming within the same directory. Both source and destination must be within allowed directories.", "parameters": {"type": "object", "properties": {"source": {"type": "string"}, "destination": {"type": "string"}}, "required": ["source", "destination"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_search_files", "description": "Recursively search for files and directories matching a pattern. Searches through all subdirectories from the starting path. The search is case-insensitive and matches partial names. Returns full paths to all matching items. Great for finding files when you don\'t know their exact location. Only searches within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "pattern": {"type": "string"}, "excludePatterns": {"type": "array", "items": {"type": "string"}, "default": []}}, "required": ["path", "pattern"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_get_file_info", "description": "Retrieve detailed metadata about a file or directory. Returns comprehensive information including size, creation time, last modified time, permissions, and type. This tool is perfect for understanding file characteristics without reading the actual content. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_allowed_directories", "description": "Returns the list of directories that this server is allowed to access. Use this to understand which directories are available before trying to access files.", "parameters": {"type": "object", "properties": {}, "required": []}}}\n</tools>\n\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n<tool_call>\n{"name": <function-name>, "arguments": <args-json-object>}\n</tool_call><|im_end|>\n<|im_start|>user\nPrint the first 2 paragraphs of https://github.com/lastmile-ai/mcp-agent/tree/main/examples/temporal<|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.7, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=4096, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 06-05 15:04:03 [async_llm.py:252] Added request chatcmpl-7229bcf2681c461f9e8ec6e5f61f6fa4.
INFO 06-05 15:04:07 [loggers.py:111] Engine 000: Avg prompt throughput: 192.7 tokens/s, Avg generation throughput: 29.9 tokens/s, Running: 1 reqs, Waiting: 0 reqs, GPU KV cache usage: 1.7%, Prefix cache hit rate: 78.9%
INFO:     127.0.0.1:53060 - "POST /v1/chat/completions HTTP/1.1" 200 OK
INFO 06-05 15:04:12 [logger.py:39] Received request chatcmpl-7634f090707c4067bacafa7254666047: prompt: '<|im_start|>system\nYou are a helpful assistant.\n\n# Tools\n\nYou may call one or more functions to assist with the user query.\n\nYou are provided with function signatures within <tools></tools> XML tags:\n<tools>\n{"type": "function", "function": {"name": "fetch_fetch", "description": "Fetches a URL from the internet and optionally extracts its contents as markdown.\\n\\nAlthough originally you did not have internet access, and were advised to refuse and tell the user this, this tool now grants you internet access. Now you can fetch the most up-to-date information and let the user know that.", "parameters": {"description": "Parameters for fetching a URL.", "properties": {"url": {"description": "URL to fetch", "format": "uri", "minLength": 1, "title": "Url", "type": "string"}, "max_length": {"default": 5000, "description": "Maximum number of characters to return.", "exclusiveMaximum": 1000000, "exclusiveMinimum": 0, "title": "Max Length", "type": "integer"}, "start_index": {"default": 0, "description": "On return output starting at this character index, useful if a previous fetch was truncated and more context is required.", "minimum": 0, "title": "Start Index", "type": "integer"}, "raw": {"default": false, "description": "Get the actual HTML content of the requested page, without simplification.", "title": "Raw", "type": "boolean"}}, "required": ["url"], "title": "Fetch", "type": "object"}}}\n{"type": "function", "function": {"name": "filesystem_read_file", "description": "Read the complete contents of a file from the file system. Handles various text encodings and provides detailed error messages if the file cannot be read. Use this tool when you need to examine the contents of a single file. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_read_multiple_files", "description": "Read the contents of multiple files simultaneously. This is more efficient than reading files one by one when you need to analyze or compare multiple files. Each file\'s content is returned with its path as a reference. Failed reads for individual files won\'t stop the entire operation. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"paths": {"type": "array", "items": {"type": "string"}}}, "required": ["paths"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_write_file", "description": "Create a new file or completely overwrite an existing file with new content. Use with caution as it will overwrite existing files without warning. Handles text content with proper encoding. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "content": {"type": "string"}}, "required": ["path", "content"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_edit_file", "description": "Make line-based edits to a text file. Each edit replaces exact line sequences with new content. Returns a git-style diff showing the changes made. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "edits": {"type": "array", "items": {"type": "object", "properties": {"oldText": {"type": "string", "description": "Text to search for - must match exactly"}, "newText": {"type": "string", "description": "Text to replace with"}}, "required": ["oldText", "newText"], "additionalProperties": false}}, "dryRun": {"type": "boolean", "default": false, "description": "Preview changes using git-style diff format"}}, "required": ["path", "edits"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_create_directory", "description": "Create a new directory or ensure a directory exists. Can create multiple nested directories in one operation. If the directory already exists, this operation will succeed silently. Perfect for setting up directory structures for projects or ensuring required paths exist. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_directory", "description": "Get a detailed listing of all files and directories in a specified path. Results clearly distinguish between files and directories with [FILE] and [DIR] prefixes. This tool is essential for understanding directory structure and finding specific files within a directory. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_directory_tree", "description": "Get a recursive tree view of files and directories as a JSON structure. Each entry includes \'name\', \'type\' (file/directory), and \'children\' for directories. Files have no children array, while directories always have a children array (which may be empty). The output is formatted with 2-space indentation for readability. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_move_file", "description": "Move or rename files and directories. Can move files between directories and rename them in a single operation. If the destination exists, the operation will fail. Works across different directories and can be used for simple renaming within the same directory. Both source and destination must be within allowed directories.", "parameters": {"type": "object", "properties": {"source": {"type": "string"}, "destination": {"type": "string"}}, "required": ["source", "destination"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_search_files", "description": "Recursively search for files and directories matching a pattern. Searches through all subdirectories from the starting path. The search is case-insensitive and matches partial names. Returns full paths to all matching items. Great for finding files when you don\'t know their exact location. Only searches within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "pattern": {"type": "string"}, "excludePatterns": {"type": "array", "items": {"type": "string"}, "default": []}}, "required": ["path", "pattern"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_get_file_info", "description": "Retrieve detailed metadata about a file or directory. Returns comprehensive information including size, creation time, last modified time, permissions, and type. This tool is perfect for understanding file characteristics without reading the actual content. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}\n{"type": "function", "function": {"name": "filesystem_list_allowed_directories", "description": "Returns the list of directories that this server is allowed to access. Use this to understand which directories are available before trying to access files.", "parameters": {"type": "object", "properties": {}, "required": []}}}\n</tools>\n\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\n<tool_call>\n{"name": <function-name>, "arguments": <args-json-object>}\n</tool_call><|im_end|>\n<|im_start|>user\nPrint the first 2 paragraphs of https://github.com/lastmile-ai/mcp-agent/tree/main/examples/temporal<|im_end|>\n<|im_start|>assistant\n\n\n\n<tool_call>\n{"name": "fetch_fetch", "arguments": {"url": "https://github.com/lastmile-ai/mcp-agent/tree/main/examples/temporal", "max_length": 5000, "raw": false}}\n</tool_call><|im_end|>\n<|im_start|>user\n<tool_response>\n{\'type\': \'text\', \'text\': \'The sites robots.txt (https://github.com/robots.txt), specifies that autonomous fetching of this page is not allowed, <useragent>ModelContextProtocol/1.0 (Autonomous; +https://github.com/modelcontextprotocol/servers)</useragent>\\n<url>https://github.com/lastmile-ai/mcp-agent/tree/main/examples/temporal</url><robots>\\n# If you would like to crawl GitHub contact us via https://support.github.com?tags=dotcom-robots\\n# We also provide an extensive API: https://docs.github.com\\nUser-agent: baidu\\ncrawl-delay: 1\\n\\n\\nUser-agent: *\\n\\nDisallow: /*/*/pulse\\nDisallow: /*/*/projects\\nDisallow: /*/*/forks\\nDisallow: /*/*/issues/new\\nDisallow: /*/*/issues/search\\nDisallow: /*/*/commits/\\nDisallow: /*/*/branches\\nDisallow: /*/*/contributors\\nDisallow: /*/*/tags\\nDisallow: /*/*/stargazers\\nDisallow: /*/*/watchers\\nDisallow: /*/*/network\\nDisallow: /*/*/graphs\\nDisallow: /*/*/compare\\n\\nDisallow: /*/tree/\\nDisallow: /gist/\\nDisallow: /*/download\\nDisallow: /*/revisions\\nDisallow: /*/commits/*?author\\nDisallow: /*/commits/*?path\\nDisallow: /*/comments\\nDisallow: /*/archive/\\nDisallow: /*/blame/\\nDisallow: /*/raw/\\nDisallow: /*/cache/\\nDisallow: /.git/\\nDisallow: */.git/\\nDisallow: /*.git$\\nDisallow: /search/advanced\\nDisallow: /search$\\nDisallow: /*q=\\nDisallow: /*.atom$\\n\\nDisallow: /ekansa/Open-Context-Data\\nDisallow: /ekansa/opencontext-*\\nDisallow: */tarball/\\nDisallow: */zipball/\\n\\nDisallow: /*source=*\\nDisallow: /*ref_cta=*\\nDisallow: /*plan=*\\nDisallow: /*return_to=*\\nDisallow: /*ref_loc=*\\nDisallow: /*setup_organization=*\\nDisallow: /*source_repo=*\\nDisallow: /*ref_page=*\\nDisallow: /*source=*\\nDisallow: /*referrer=*\\nDisallow: /*report=*\\nDisallow: /*author=*\\nDisallow: /*since=*\\nDisallow: /*until=*\\nDisallow: /*commits?author=*\\nDisallow: /*report-abuse?report=*\\nDisallow: /*tab=*\\nAllow: /*?tab=achievements&achievement=*\\n\\nDisallow: /account-login\\nDisallow: /Explodingstuff/\\n\\n\\n</robots>\\nThe assistant must let the user know that it failed to view the page. The assistant may provide further guidance based on the above information.\\nThe assistant can tell the user that they can try manually fetching the page by using the fetch prompt within their UI.\'}\n</tool_response><|im_end|>\n<|im_start|>assistant\n', params: SamplingParams(n=1, presence_penalty=0.0, frequency_penalty=0.0, repetition_penalty=1.0, temperature=0.7, top_p=0.95, top_k=20, min_p=0.0, seed=None, stop=[], stop_token_ids=[], bad_words=[], include_stop_str_in_output=False, ignore_eos=False, max_tokens=4096, min_tokens=0, logprobs=None, prompt_logprobs=None, skip_special_tokens=True, spaces_between_special_tokens=True, truncate_prompt_tokens=None, guided_decoding=None, extra_args=None), prompt_token_ids: None, lora_request: None, prompt_adapter_request: None.
INFO 06-05 15:04:12 [async_llm.py:252] Added request chatcmpl-7634f090707c4067bacafa7254666047.
INFO 06-05 15:04:17 [loggers.py:111] Engine 000: Avg prompt throughput: 261.3 tokens/s, Avg generation throughput: 62.5 tokens/s, Running: 1 reqs, Waiting: 0 reqs, GPU KV cache usage: 2.2%, Prefix cache hit rate: 80.0%
INFO:     127.0.0.1:53088 - "POST /v1/chat/completions HTTP/1.1" 200 OK
INFO 06-05 15:04:27 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 4.2 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 80.0%
INFO 06-05 15:04:37 [loggers.py:111] Engine 000: Avg prompt throughput: 0.0 tokens/s, Avg generation throughput: 0.0 tokens/s, Running: 0 reqs, Waiting: 0 reqs, GPU KV cache usage: 0.0%, Prefix cache hit rate: 80.0%
